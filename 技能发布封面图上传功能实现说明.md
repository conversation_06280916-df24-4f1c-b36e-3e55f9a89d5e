# 技能发布封面图上传功能实现说明

## 功能概述

为技能发布页面实现了完整的图片上传功能，包括封面图上传和服务图片上传，使用现有的文件上传接口。

## 后端实现

### 1. 文件用途枚举扩展

在 `src/main/java/com/school/enums/FilePurposeEnum.java` 中新增了两个枚举值：

```java
/** 技能封面图 */
SKILL_COVER("skillCover", "技能封面图", Arrays.asList("jpg", "jpeg", "png", "webp")),
/** 技能服务图片 */
SKILL_IMAGE("skillImage", "技能服务图片", Arrays.asList("jpg", "jpeg", "png", "webp")),
```

**特点**：
- 支持常见的图片格式：JPG、JPEG、PNG、WebP
- 专门为技能服务设计的文件用途分类
- 与现有的文件上传控制器完全兼容

### 2. 文件上传接口

使用现有的 `FileUploadController.java` 接口：
- **接口地址**：`POST /upload`
- **参数**：
  - `file`：上传的文件
  - `purpose`：文件用途（skillCover 或 skillImage）
- **返回**：文件访问URL

## 前端实现

### 1. 文件用途枚举同步

在 `schoolweb/src/enums/filePurpose.ts` 中新增：

```typescript
/** 技能封面图 */
SKILL_COVER = 'skillCover',
/** 技能服务图片 */
SKILL_IMAGE = 'skillImage',
```

### 2. 封面图上传功能

**功能特点**：
- 单张图片上传
- 实时预览
- 支持删除和重新上传
- 文件类型限制：JPG、PNG、WebP
- 推荐尺寸：800x400px

**实现细节**：
```typescript
async function handleUploadCover(options: any) {
  const fileUrl = await uploadFile(file.file as File, FilePurpose.SKILL_COVER);
  if (fileUrl) {
    coverUrl.value = fileUrl;
    // 将封面图添加到images数组的第一位
    if (!formData.images.includes(fileUrl)) {
      formData.images.unshift(fileUrl);
    }
  }
}
```

### 3. 服务图片上传功能

**功能特点**：
- 多张图片上传（最多5张）
- 网格布局预览
- 悬停显示删除按钮
- 文件类型限制：JPG、PNG、WebP
- 推荐尺寸：800x600px

**实现细节**：
```typescript
async function handleUploadImage(options: any) {
  const fileUrl = await uploadFile(file.file as File, FilePurpose.SKILL_IMAGE);
  if (fileUrl) {
    formData.images.push(fileUrl);
  }
}
```

### 4. 图片管理逻辑

**封面图与服务图片的关联**：
- 封面图会自动添加到服务图片列表的第一位
- 删除封面图时，也会从服务图片列表中移除
- 删除服务图片时，如果是封面图，会同时清空封面图

**删除逻辑**：
```typescript
// 删除封面图
function removeCoverImage() {
  if (coverUrl.value) {
    const index = formData.images.indexOf(coverUrl.value);
    if (index > -1) {
      formData.images.splice(index, 1);
    }
    coverUrl.value = '';
  }
}

// 删除服务图片
function removeImage(index: number) {
  const imageUrl = formData.images[index];
  if (imageUrl === coverUrl.value) {
    coverUrl.value = '';
  }
  formData.images.splice(index, 1);
}
```

## 界面设计

### 1. 封面图上传区域

```vue
<NFormItem label="封面图" path="coverUrl">
  <div class="flex items-start">
    <NUpload
      action="#"
      :max="1"
      :show-file-list="false"
      :custom-request="handleUploadCover"
      accept="image/jpeg,image/jpg,image/png,image/webp"
    >
      <NButton :loading="uploadCoverLoading">
        上传封面图
      </NButton>
    </NUpload>
    
    <!-- 预览区域 -->
    <div v-if="coverUrl" class="ml-16px">
      <div class="h-100px w-180px relative">
        <img :src="coverUrl" class="w-full h-full object-cover" />
        <NButton @click="removeCoverImage">删除</NButton>
      </div>
    </div>
  </div>
</NFormItem>
```

### 2. 服务图片上传区域

```vue
<NFormItem label="服务图片" path="images">
  <div class="w-full">
    <NUpload
      :max="5"
      :custom-request="handleUploadImage"
      accept="image/jpeg,image/jpg,image/png,image/webp"
      multiple
    >
      <NButton :disabled="formData.images.length >= 5">
        添加服务图片 ({{ formData.images.length }}/5)
      </NButton>
    </NUpload>
    
    <!-- 图片网格预览 -->
    <div class="grid grid-cols-3 gap-12px">
      <div v-for="(image, index) in formData.images" :key="index">
        <img :src="image" class="w-full h-full object-cover" />
        <NButton @click="removeImage(index)">删除</NButton>
      </div>
    </div>
  </div>
</NFormItem>
```

## 技术特点

### 1. 文件类型验证
- **前端**：通过 `accept` 属性限制文件选择
- **后端**：通过枚举配置验证文件扩展名

### 2. 用户体验优化
- **加载状态**：上传过程中显示loading状态
- **实时预览**：上传成功后立即显示预览
- **交互反馈**：悬停效果、删除确认等
- **数量限制**：封面图1张，服务图片最多5张

### 3. 错误处理
- **网络错误**：捕获上传失败并显示错误信息
- **文件类型错误**：后端验证并返回具体错误信息
- **文件大小限制**：可在后端配置文件大小限制

## 数据流程

1. **用户选择文件** → 前端验证文件类型
2. **调用上传API** → 后端验证并保存文件
3. **返回文件URL** → 前端更新界面和数据
4. **提交表单** → 将图片URL数组发送到后端

## 使用说明

### 1. 上传封面图
1. 点击"上传封面图"按钮
2. 选择图片文件（JPG、PNG、WebP）
3. 等待上传完成
4. 查看预览效果
5. 可点击删除按钮重新上传

### 2. 上传服务图片
1. 点击"添加服务图片"按钮
2. 可一次选择多张图片
3. 最多上传5张图片
4. 悬停在图片上可看到删除按钮
5. 点击删除按钮移除不需要的图片

### 3. 注意事项
- 封面图会自动成为第一张服务图片
- 删除封面图不会影响其他服务图片
- 建议上传高质量的图片以获得更好的展示效果
- 图片文件大小建议控制在2MB以内

## 后续优化建议

1. **图片压缩**：前端上传前自动压缩图片
2. **拖拽上传**：支持拖拽文件到上传区域
3. **图片裁剪**：提供在线图片裁剪功能
4. **批量操作**：支持批量删除图片
5. **图片排序**：支持拖拽调整图片顺序
