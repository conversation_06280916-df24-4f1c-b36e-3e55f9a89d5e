package com.school;

import cn.dev33.satoken.secure.BCrypt;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.UnsupportedEncodingException;

/**
 * 2024/9/11
 * 栋dong
 */
@SpringBootTest
public class captchaTest {

    @Test
    public void test() throws UnsupportedEncodingException {
        //$2a$06$3FbwCUNcAD6QfWSR4Hit9O9laSe6fGN7/Q.HRiqeby93CofwKCYES
        System.out.println(BCrypt.hashpw("2b52c119-148", BCrypt.gensalt(6)));
//        JSONObject json_param = new JSONObject();
//        json_param.put("TakeNum",30);
//        json_param.put("SearchText","220911123");
//        String param = Base64.getEncoder().encodeToString(URLEncoder.encode(json_param.toString(), StandardCharsets.UTF_8.toString()).getBytes());//先Escape编码，再base64
//        System.out.println(param);
    }
}
