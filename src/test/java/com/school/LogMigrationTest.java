package com.school;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

@SpringBootTest
public class LogMigrationTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;
    
    private static final String DEFAULT_ERROR_MSG = "出现问题，请寻求管理员帮助";
    private static final int MSG_MAX_LENGTH = 255;

//    @Test
//    public void migrateHistoricalLogs() {
//        // 1. 创建新表
//        jdbcTemplate.execute("""
//            CREATE TABLE IF NOT EXISTS sys_log_new (
//                id BIGINT PRIMARY KEY AUTO_INCREMENT,
//                operation_module VARCHAR(100),
//                operation_type VARCHAR(100),
//                operation_desc VARCHAR(500),
//                request_url VARCHAR(500),
//                request_method VARCHAR(20),
//                request_params LONGTEXT,
//                request_headers LONGTEXT,
//                operation_ip VARCHAR(100),
//                response_code INT,
//                response_msg VARCHAR(255),
//                response_data LONGTEXT,
//                user_id BIGINT,
//                username VARCHAR(100),
//                operation_time DATETIME,
//                execution_time BIGINT
//            )
//        """);
//
//        // 2. 获取所有历史日志
//        List<Map<String, Object>> oldLogs = jdbcTemplate.queryForList("SELECT * FROM sys_log");
//
//        // 3. 迁移数据
//        String insertSql = """
//            INSERT INTO sys_log_new (
//                operation_module, operation_type, operation_desc,
//                request_url, request_method, request_params,
//                request_headers, operation_ip, response_code,
//                response_msg, response_data, user_id,
//                username, operation_time, execution_time
//            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
//        """;
//
//        int successCount = 0;
//        int failCount = 0;
//        int truncatedCount = 0;
//
//        for (Map<String, Object> oldLog : oldLogs) {
//            try {
//                // 处理响应结果
//                String responseResult = (String) oldLog.get("response_result");
//                Integer responseCode = 200;
//                String responseMsg = "";
//                String responseData = "";
//
//                if (responseResult != null) {
//                    try {
//                        // 尝试解析为Map格式
//                        Map<String, Object> responseMap = objectMapper.readValue(responseResult, Map.class);
//                        if (responseMap.containsKey("code")) {
//                            responseCode = responseMap.containsKey("code") ? ((Number) responseMap.get("code")).intValue() : 0;
//                            responseMsg = (String) responseMap.get("msg");
//
//                            // 处理响应消息
//                            if (responseMsg == null) {
//                                responseMsg = "";
//                            } else if (responseMsg.length() > MSG_MAX_LENGTH) {
//                                responseMsg = DEFAULT_ERROR_MSG;
//                                truncatedCount++;
//                            }
//
//                            responseData = objectMapper.writeValueAsString(responseMap.get("data"));
//                        } else {
//                            responseData = responseResult;
//                        }
//                    } catch (Exception e) {
//                        // 如果解析失败，将原始数据作为data
//                        responseData = responseResult;
//                    }
//                }
//
//                // 处理时间字段
//                Object operationTimeObj = oldLog.get("operation_time");
//                LocalDateTime operationTime = null;
//                if (operationTimeObj instanceof LocalDateTime) {
//                    operationTime = (LocalDateTime) operationTimeObj;
//                } else if (operationTimeObj != null) {
//                    // 如果不是 LocalDateTime，尝试转换
//                    operationTime = LocalDateTime.parse(operationTimeObj.toString());
//                }
//
//                // 执行插入
//                jdbcTemplate.update(
//                    insertSql,
//                    oldLog.get("operation_module"),
//                    oldLog.get("operation_type"),
//                    oldLog.get("operation_desc"),
//                    oldLog.get("request_url"),
//                    oldLog.get("request_method"),
//                    oldLog.get("request_params"),
//                    oldLog.get("request_headers"),
//                    oldLog.get("operation_ip"),
//                    responseCode,
//                    responseMsg,
//                    responseData,
//                    oldLog.get("user_id"),
//                    oldLog.get("username"),
//                    operationTime,
//                    oldLog.get("execution_time")
//                );
//
//                successCount++;
//                if (successCount % 100 == 0) {
//                    System.out.println("已成功迁移 " + successCount + " 条记录");
//                }
//
//            } catch (Exception e) {
//                failCount++;
//                System.err.println("迁移日志失败，ID: " + oldLog.get("id"));
//                e.printStackTrace();
//            }
//        }
//
//        // 4. 验证数据
//        long oldCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM sys_log", Long.class);
//        long newCount = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM sys_log_new", Long.class);
//
//        System.out.println("\n迁移统计：");
//        System.out.println("原表记录数: " + oldCount);
//        System.out.println("新表记录数: " + newCount);
//        System.out.println("成功迁移: " + successCount + " 条");
//        System.out.println("失败记录: " + failCount + " 条");
//        System.out.println("消息被截断: " + truncatedCount + " 条");
//
//        if (oldCount == newCount) {
//            System.out.println("\n数据迁移成功！");
//
//            // 5. 提供回滚和切换的SQL
//            System.out.println("\n执行以下SQL完成切换：");
//            System.out.println("-- 备份原表");
//            System.out.println("RENAME TABLE sys_log TO sys_log_backup;");
//            System.out.println("-- 将新表改名为正式表");
//            System.out.println("RENAME TABLE sys_log_new TO sys_log;");
//        } else {
//            System.out.println("\n警告：数据条数不一致，请检查迁移日志！");
//        }
//    }
} 