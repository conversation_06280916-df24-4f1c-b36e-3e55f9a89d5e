package com.school.service.impl;

import com.school.service.FileUploadService;
import com.school.enums.FilePurposeEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Service
public class FileUploadServiceImpl implements FileUploadService {

    @Value("${file.upload.path}")
    private String uploadPath;

    @Override
    public String saveFile(MultipartFile file, FilePurposeEnum purpose) throws Exception {
        // 获取文件原始名称
        String originalFilename = file.getOriginalFilename();
        // 获取文件扩展名
        String extension = originalFilename != null ? originalFilename.substring(originalFilename.lastIndexOf(".")) : "";
        
        // 生成新的文件名（UUID + 时间戳 + 原扩展名）
        String fileName = UUID.randomUUID().toString().replace("-", "") + 
                         LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + 
                         extension;
        
        // 根据用途创建对应的子目录
        String subDir = purpose.getCode();
        Path purposePath = Paths.get(uploadPath, subDir);
        
        // 如果目录不存在则创建
        if (!Files.exists(purposePath)) {
            Files.createDirectories(purposePath);
        }
        
        // 构建完整的文件保存路径
        Path filePath = purposePath.resolve(fileName);
        
        // 保存文件
        file.transferTo(filePath.toFile());
        
        // 返回文件的相对访问路径（用于前端访问）
        return String.format("/files/%s/%s", subDir, fileName);
    }
} 