package com.school.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.school.entity.Roles;
import com.school.entity.SysLog;
import com.school.entity.Users;
import com.school.entity.vo.PageResult;
import com.school.entity.vo.UserInfoVo;
import com.school.entity.vo.UserListVo;
import com.school.entity.vo.UserVo;
import com.school.enums.UserStatus;
import com.school.mapper.PermissionsMapper;
import com.school.mapper.RolesDao;
import com.school.mapper.SysLogMapper;
import com.school.mapper.UserMapper;
import com.school.service.FileUploadService;
import com.school.service.RolesService;
import com.school.service.UserService;
import com.school.service.VerificationCodeService;
import com.school.service.EmailService;
import com.school.util.ExcelTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import com.school.entity.dto.IdentityVerifyDto;
import com.school.entity.Audits;
import java.time.LocalDateTime;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.school.service.AuditService;

/**
 * (User)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-12 21:36:45
 */
@Slf4j
@Service("userService")
public class UserServiceImpl extends ServiceImpl<UserMapper, Users> implements UserService {
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private RolesDao rolesMapper;
    @Autowired
    private PermissionsMapper permissionsMapper;
    @Autowired
    private SysLogMapper sysLogMapper;
    @Autowired
    private FileUploadService fileUploadService;
    @Autowired
    private RolesService rolesService;
    @Autowired
    private VerificationCodeService verificationCodeService;
    @Autowired
    private EmailService emailService;
    @Autowired
    private AuditService auditService;

    /**
     * 生成强密码
     * 包含大小写字母、数字和特殊字符
     *
     * @return 生成的强密码
     */
    private String generateStrongPassword() {
        String upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowerCase = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "0123456789";
        String specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        StringBuilder password = new StringBuilder();
        // 确保至少包含一个大写字母
        password.append(upperCase.charAt((int) (Math.random() * upperCase.length())));
        // 确保至少包含一个小写字母
        password.append(lowerCase.charAt((int) (Math.random() * lowerCase.length())));
        // 确保至少包含一个数字
        password.append(numbers.charAt((int) (Math.random() * numbers.length())));
        // 确保至少包含一个特殊字符
        password.append(specialChars.charAt((int) (Math.random() * specialChars.length())));

        // 添加额外的随机字符，直到达到12位
        String allChars = upperCase + lowerCase + numbers + specialChars;
        while (password.length() < 12) {
            password.append(allChars.charAt((int) (Math.random() * allChars.length())));
        }

        // 打乱密码字符顺序
        String shuffled = password.toString();
        char[] shuffledChars = shuffled.toCharArray();
        for (int i = shuffledChars.length - 1; i > 0; i--) {
            int j = (int) (Math.random() * (i + 1));
            char temp = shuffledChars[i];
            shuffledChars[i] = shuffledChars[j];
            shuffledChars[j] = temp;
        }

        return new String(shuffledChars);
    }

    @Override
    public Integer login(Users user) {
        LambdaQueryWrapper<Users> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Users::getEmail, user.getEmail());
        Users loginUser = userMapper.selectOne(lambdaQueryWrapper);

        if (loginUser != null) {
            if (BCrypt.checkpw(user.getPassword(), loginUser.getPassword())) {
                // 验证用户状态
                if (UserStatus.NORMAL.getCode().equals(loginUser.getStatus()) ||
                    UserStatus.PENDING_REVIEW.getCode().equals(loginUser.getStatus()) ||
                    UserStatus.PENDING_UPLOAD.getCode().equals(loginUser.getStatus())) {
                    // 更新最后登录时间
                    updateLastLoginTime(loginUser.getUserId());
                    return loginUser.getUserId();
                } else {
                    UserStatus status = UserStatus.getByCode(loginUser.getStatus());
                    throw new RuntimeException("用户" + status.getDescription() + "状态，无法登录");
                }
            } else {
                throw new RuntimeException("密码错误");
            }
        }
        throw new RuntimeException("用户不存在");
    }

    // 添加更新最后登录时间的私有方法
    private void updateLastLoginTime(Integer userId) {
        try {
            Users updateUser = new Users();
            updateUser.setUserId(userId);
            updateUser.setLastLoginTime(new Date()); // 设置当前时间为最后登录时间
            userMapper.updateById(updateUser);
        } catch (Exception e) {
            // 记录错误但不影响登录流程
            log.error("更新用户最后登录时间失败：" + e.getMessage(), e);
        }
    }

    // 读取Excel文件并生成随机初始密码，同时将数据插入数据库，并写入到新的Excel文件
    public SaResult importUsersFromExcelAndSave(MultipartFile file) throws Exception {
        List<Users> users = ExcelTool.readExcel(file, Users.class);
        List<Users> result = new ArrayList<>();

        for (Users user : users) {
            user.setEmail(user.getStuId() + "@dmsy.me");
            String password = generateStrongPassword();
            user.setPassword(BCrypt.hashpw(password, BCrypt.gensalt(6)));//加盐加密
            user.setStatus(1); // 设置默认状态为正常

            userMapper.insert(user);
            user.setPassword(password); // 设置明文密码用于返回
            result.add(user);
        }

        // 使用 FileUploadService 保存生成的 Excel 文件
        String outputPath = ExcelTool.writeExcel(result, "账号密码", Users.class, fileUploadService);
        return SaResult.data(outputPath);
    }

    @Override
    public UserInfoVo getUserInfo(int loginId) {
        Users users = userMapper.selectById(loginId);
        UserInfoVo userInfoVo = new UserInfoVo();
        BeanUtils.copyProperties(users, userInfoVo);
        userInfoVo.setRoles(rolesMapper.getall(String.valueOf(loginId)));
        return userInfoVo;
    }

    @Override
    public List<UserVo> searchUsers(String query) {
        return userMapper.searchUsers(query);
    }

    @Override
    public List<Map<String, Object>> batchGetUserBasicInfo(List<String> userIds) {
        return userMapper.selectBasicInfoByIds(userIds);
    }

    @Override
    public PageResult<UserListVo> getUserList(Integer page, Integer pageSize, String query, Integer status) {
        // 计算偏移量
        Integer offset = (page - 1) * pageSize;

        // 查询总记录数
        Integer total = userMapper.selectUserTotal(query, status);

        // 查询当前页数据
        List<Users> users = userMapper.selectUserPage(offset, pageSize, query, status);

        // 转换为 UserListVo
        List<UserListVo> voList = users.stream().map(user -> {
            UserListVo vo = new UserListVo();
            BeanUtils.copyProperties(user, vo);
            // 获取用户角色
            vo.setRoles(rolesMapper.getall(String.valueOf(user.getUserId())));
            // 获取用户权限
            vo.setPermissions(permissionsMapper.getall(String.valueOf(user.getUserId())));
            return vo;
        }).collect(Collectors.toList());

        // 返回分页结果
        return new PageResult<>(voList, total, pageSize, page);
    }

    @Override
    public boolean updateUserInfo(UserListVo userVo) {
        Users user = new Users();
        BeanUtils.copyProperties(userVo, user);
        
        // 如果有密码，进行加密
        if (user.getPassword() != null) {
            user.setPassword(BCrypt.hashpw(user.getPassword(), BCrypt.gensalt(6)));
            StpUtil.logout(user.getUserId());
        }

        // 检查一卡通号和一卡通照片更新
        if (userVo.getCardId() != null || userVo.getCardPhoto() != null) {
            Users existingUser = userMapper.selectOne(
                new LambdaQueryWrapper<Users>()
                    .eq(Users::getUserId, userVo.getUserId())
            );
            boolean isFirstLogin = existingUser.getIsFirstLogin() != null && existingUser.getIsFirstLogin() == 1;
            // 只有超级管理员或首次登录时才能设置一卡通号和一卡通照片
            if (!StpUtil.hasRole("superAdmin") && !isFirstLogin) {
                throw new RuntimeException("仅首次登录或管理员可设置一卡通号和一卡通照片");
            }
        }

        // 更新用户基本信息
        boolean success = userMapper.updateById(user) > 0;
        
        // 如果有角色信息，更新用户角色
        if (success && userVo.getRoles() != null && !userVo.getRoles().isEmpty()) {
            // 先删除用户现有角色
            rolesMapper.deleteUserRoles(String.valueOf(user.getUserId()));
            // 添加新角色
            for (String role : userVo.getRoles()) {
                rolesMapper.addUserRole(String.valueOf(user.getUserId()), role);
            }
        }
        
        return success;
    }

    /**
     * 检查用户是否是首次登录
     * 通过查询系统日志中是否存在该用户的登录记录来判断
     *
     * @param userId 用户ID
     * @return 是否首次登录
     */
    @Override
    public boolean isFirstLogin(Integer userId) {
        // 直接从用户表查询is_first_login字段
        Users user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 返回用户的首次登录状态：1-是首次登录，0-不是首次登录
        return user.getIsFirstLogin() == 1;
    }
    
    /**
     * 更新用户首次登录状态
     *
     * @param userId 用户ID
     * @return 是否更新成功
     */
    @Override
    public boolean updateFirstLoginStatus(Integer userId) {
        Users user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }
        
        user.setIsFirstLogin(0); // 设置为非首次登录
        int result = userMapper.updateById(user);
        
        return result > 0;
    }

    /**
     * 更新用户密码
     *
     * @param userId      用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    @Override
    public boolean updatePassword(Integer userId, String oldPassword, String newPassword) {
        // 获取用户信息
        Users user = userMapper.selectById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 验证旧密码是否正确
        if (!user.getPassword().equals(DigestUtils.md5DigestAsHex(oldPassword.getBytes()))) {
            throw new RuntimeException("旧密码不正确");
        }

        // 更新新密码
        Users updateUser = new Users();
        updateUser.setUserId(userId);
        updateUser.setPassword(DigestUtils.md5DigestAsHex(newPassword.getBytes()));

        return userMapper.updateById(updateUser) > 0;
    }

    @Override
    public List<String> getAllRoles() {
        return rolesService.list().stream()
                .map(Roles::getRoleName)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean sendEmailVerificationCode(String email) {
        try {
            // 检查是否是注册验证码
            boolean isRegistration = !isEmailExist(email);
            String type = isRegistration ? "register" : "login";
            
            // 生成验证码（同时存入Redis）
            String code = verificationCodeService.generateCode(email, type);
            
            // 根据类型发送不同的验证码邮件
            if (isRegistration) {
                emailService.sendRegisterCode(email, code);
            } else {
                emailService.sendLoginCode(email, code);
            }
            
            return true;
        } catch (Exception e) {
            log.error("发送邮箱验证码失败", e);
            return false;
        }
    }
    
    @Override
    public Integer loginWithCode(String email, String code) {
        try {
            // 验证验证码
            boolean isValid = verificationCodeService.verifyCode(email, code, "login");
            if (!isValid) {
                throw new RuntimeException("验证码错误或已过期");
            }
            
            // 验证成功后，使验证码失效
            verificationCodeService.invalidateCode(email, "login");
            
            // 查询用户
            LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Users::getEmail, email);
            Users user = userMapper.selectOne(wrapper);
            
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 验证用户状态
            if (UserStatus.NORMAL.getCode().equals(user.getStatus()) ||
                UserStatus.PENDING_REVIEW.getCode().equals(user.getStatus()) ||
                UserStatus.PENDING_UPLOAD.getCode().equals(user.getStatus())) {
                // 更新最后登录时间
                updateLastLoginTime(user.getUserId());
                return user.getUserId();
            } else {
                UserStatus status = UserStatus.getByCode(user.getStatus());
                throw new RuntimeException("用户" + status.getDescription() + "状态，无法登录");
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("邮箱验证码登录失败", e);
            throw new RuntimeException("登录失败，请稍后重试");
        }
    }
    
    @Override
    public Integer register(Users user, String code) {
        try {
            // 验证验证码
            boolean isValid = verificationCodeService.verifyCode(user.getEmail(), code, "register");
            if (!isValid) {
                throw new RuntimeException("验证码错误或已过期");
            }
            
            // 验证成功后，使验证码失效
            verificationCodeService.invalidateCode(user.getEmail(), "register");
            
            // 检查邮箱是否已存在
            if (isEmailExist(user.getEmail())) {
                throw new RuntimeException("该邮箱已被注册");
            }
            
            // 设置默认值
            user.setStatus(UserStatus.PENDING_UPLOAD.getCode()); // 设置用户状态为待上传证件
            user.setStatusReason("新用户注册，待上传证件");
            user.setStatusUpdateTime(new Date());
            // 密码加密
            String hashedPassword = BCrypt.hashpw(user.getPassword(), BCrypt.gensalt(6));
            user.setPassword(hashedPassword);
            
            // 保存用户
            userMapper.insert(user);
            
            // 为新用户分配默认角色（学生角色）
            rolesMapper.insertUserRole(user.getUserId(), getRoleIdByName("student"));
            
            return user.getUserId();
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户注册失败", e);
            throw new RuntimeException("注册失败，请稍后重试");
        }
    }
    
    /**
     * 通过角色名获取角色ID
     */
    private Integer getRoleIdByName(String roleName) {
        return rolesMapper.getRoleIdByName(roleName);
    }
    
    @Override
    public boolean isEmailExist(String email) {
        LambdaQueryWrapper<Users> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Users::getEmail, email);
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean verifyIdentity(IdentityVerifyDto identityVerifyDto, Integer userId) {
        // 验证必要字段是否为空
        if (identityVerifyDto.getName() == null || identityVerifyDto.getName().isEmpty()
                || identityVerifyDto.getStuId() == null || identityVerifyDto.getStuId().isEmpty()
                || identityVerifyDto.getCardId() == null || identityVerifyDto.getCardId().isEmpty()
                || identityVerifyDto.getAcademy() == null || identityVerifyDto.getAcademy().isEmpty()
                || identityVerifyDto.getUserType() == null
                || identityVerifyDto.getCardPhoto() == null || identityVerifyDto.getCardPhoto().isEmpty()) {
            throw new IllegalArgumentException("所有字段均为必填");
        }

        try {
            // 更新用户信息
            Users user = this.getById(userId);
            if (user == null) {
                return false;
            }

            user.setName(identityVerifyDto.getName());
            user.setStuId(identityVerifyDto.getStuId());
            user.setCardId(identityVerifyDto.getCardId());
            user.setAcademy(identityVerifyDto.getAcademy());
            user.setCardPhoto(identityVerifyDto.getCardPhoto());
            // 设置用户状态为待审核
            user.setStatus(UserStatus.PENDING_REVIEW.getCode()); // 2-待审核
            user.setStatusReason("用户已上传证件，等待审核");
            user.setStatusUpdateTime(new Date()); // 使用java.util.Date

            boolean updateResult = this.updateById(user);
            if (!updateResult) {
                return false;
            }

            // 创建审核记录
            Audits audit = new Audits();
            audit.setTargetId(userId.longValue());
            audit.setTargetType("user");
            audit.setTargetName(user.getName());
            audit.setStatus(0); // 0-待审核
            audit.setSubmitTime(LocalDateTime.now());
            audit.setContent("用户申请身份认证，学号/工号：" + user.getStuId() + "，一卡通号：" + user.getCardId());
            
            // 保存审核记录
            boolean auditResult = auditService.save(audit);
            return auditResult;
        } catch (Exception e) {
            log.error("用户身份验证失败", e);
            return false;
        }
    }

    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 新状态
     * @param reason 状态变更原因
     * @return 是否更新成功
     */
    @Override
    public boolean updateUserStatus(Integer userId, Integer status, String reason) {
        try {
            Users user = this.getById(userId);
            if (user == null) {
                return false;
            }
            
            user.setStatus(status);
            user.setStatusReason(reason);
            user.setStatusUpdateTime(new Date());
            
            return this.updateById(user);
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return false;
        }
    }

    @Override
    public Integer getVerificationStatus(Integer userId) {
        // 获取用户信息
        Users user = this.getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 获取用户状态
        Integer status = user.getStatus();
        
        // 根据状态返回认证状态
        // 状态为1时表示已认证通过，状态为2表示待审核，状态为5表示待上传证件
        if (UserStatus.NORMAL.getCode().equals(status)) {
            return UserStatus.NORMAL.getCode(); // 1-正常/已认证
        } else if (UserStatus.PENDING_REVIEW.getCode().equals(status)) {
            return UserStatus.PENDING_REVIEW.getCode(); // 2-待审核
        } else if (UserStatus.PENDING_UPLOAD.getCode().equals(status)) {
            return UserStatus.PENDING_UPLOAD.getCode(); // 5-待上传证件
        } else {
            // 其他状态（3-已禁用，4-已注销）视为未认证
            return UserStatus.PENDING_UPLOAD.getCode(); // 默认返回待上传证件状态
        }
    }
}

