package com.school.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.school.dto.SkillPublishRequest;
import com.school.dto.SkillServiceDTO;
import com.school.entity.SkillImage;
import com.school.entity.SkillService;
import com.school.mapper.SkillImageMapper;
import com.school.mapper.SkillServiceMapper;
import com.school.service.SkillServiceService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能服务业务实现类
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Service
public class SkillServiceServiceImpl implements SkillServiceService {
    
    @Autowired
    private SkillServiceMapper skillServiceMapper;
    
    @Autowired
    private SkillImageMapper skillImageMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public IPage<SkillServiceDTO> getSkillServicePage(Integer page, Integer size, String keyword, 
                                                     Integer categoryId, BigDecimal priceMin, 
                                                     BigDecimal priceMax, String sortBy) {
        Page<Map<String, Object>> pageParam = new Page<>(page, size);
        IPage<Map<String, Object>> resultPage = skillServiceMapper.selectSkillServicePage(
                pageParam, keyword, categoryId, priceMin, priceMax, sortBy);
        
        // 转换为DTO
        IPage<SkillServiceDTO> dtoPage = new Page<>(page, size, resultPage.getTotal());
        List<SkillServiceDTO> dtoList = resultPage.getRecords().stream()
                .map(this::convertMapToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }
    
    @Override
    public SkillServiceDTO getSkillServiceDetail(Long serviceId) {
        // 增加浏览次数
        incrementViewCount(serviceId);
        
        // 查询详情
        Map<String, Object> detailMap = skillServiceMapper.selectSkillServiceDetail(serviceId);
        if (detailMap == null) {
            return null;
        }
        
        SkillServiceDTO dto = convertMapToDTO(detailMap);
        
        // 查询图片列表
        List<SkillImage> images = skillImageMapper.selectByServiceId(serviceId);
        if (images != null && !images.isEmpty()) {
            List<String> imageUrls = images.stream()
                    .map(SkillImage::getImageUrl)
                    .collect(Collectors.toList());
            dto.setImages(imageUrls);
        }
        
        return dto;
    }
    
    @Override
    @Transactional
    public Long publishSkillService(Integer userId, SkillPublishRequest request) {
        // 验证线下服务必须填写地点
        if (request.getServiceType() == 2 && !StringUtils.hasText(request.getLocation())) {
            throw new IllegalArgumentException("线下服务必须填写服务地点");
        }
        
        // 创建技能服务实体
        SkillService skillService = new SkillService();
        BeanUtils.copyProperties(request, skillService);
        skillService.setUserId(userId);
        skillService.setStatus(1); // 待审核状态
        skillService.setRatingAvg(BigDecimal.ZERO);
        skillService.setRatingCount(0);
        skillService.setOrderCount(0);
        skillService.setViewCount(0);
        skillService.setCreatedTime(new Date());
        skillService.setUpdatedTime(new Date());
        
        // 处理标签
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            try {
                skillService.setTags(objectMapper.writeValueAsString(request.getTags()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("标签数据处理失败", e);
            }
        }
        
        // 保存技能服务
        skillServiceMapper.insert(skillService);
        Long serviceId = skillService.getServiceId();
        
        // 保存图片
        if (request.getImages() != null && !request.getImages().isEmpty()) {
            saveServiceImages(serviceId, request.getImages());
        }
        
        return serviceId;
    }
    
    @Override
    @Transactional
    public boolean updateSkillService(Long serviceId, Integer userId, SkillPublishRequest request) {
        // 检查权限
        if (!checkUserPermission(serviceId, userId)) {
            throw new IllegalArgumentException("无权限操作该服务");
        }
        
        // 验证线下服务必须填写地点
        if (request.getServiceType() == 2 && !StringUtils.hasText(request.getLocation())) {
            throw new IllegalArgumentException("线下服务必须填写服务地点");
        }
        
        // 更新技能服务
        SkillService skillService = new SkillService();
        BeanUtils.copyProperties(request, skillService);
        skillService.setServiceId(serviceId);
        skillService.setUpdatedTime(new Date());
        
        // 处理标签
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            try {
                skillService.setTags(objectMapper.writeValueAsString(request.getTags()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("标签数据处理失败", e);
            }
        }
        
        int result = skillServiceMapper.updateById(skillService);
        
        // 更新图片
        if (request.getImages() != null) {
            // 删除原有图片
            QueryWrapper<SkillImage> wrapper = new QueryWrapper<>();
            wrapper.eq("service_id", serviceId);
            skillImageMapper.delete(wrapper);
            
            // 保存新图片
            if (!request.getImages().isEmpty()) {
                saveServiceImages(serviceId, request.getImages());
            }
        }
        
        return result > 0;
    }
    
    @Override
    @Transactional
    public boolean deleteSkillService(Long serviceId, Integer userId) {
        // 检查权限
        if (!checkUserPermission(serviceId, userId)) {
            throw new IllegalArgumentException("无权限操作该服务");
        }
        
        // 软删除：更新状态为已删除
        SkillService skillService = new SkillService();
        skillService.setServiceId(serviceId);
        skillService.setStatus(4); // 已删除状态
        skillService.setUpdatedTime(new Date());
        
        return skillServiceMapper.updateById(skillService) > 0;
    }
    
    @Override
    public IPage<SkillServiceDTO> getUserSkillServices(Integer userId, Integer page, Integer size, Integer status) {
        Page<Map<String, Object>> pageParam = new Page<>(page, size);
        IPage<Map<String, Object>> resultPage = skillServiceMapper.selectUserSkillServices(pageParam, userId, status);
        
        // 转换为DTO
        IPage<SkillServiceDTO> dtoPage = new Page<>(page, size, resultPage.getTotal());
        List<SkillServiceDTO> dtoList = resultPage.getRecords().stream()
                .map(this::convertMapToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);
        
        return dtoPage;
    }
    
    @Override
    public void incrementViewCount(Long serviceId) {
        skillServiceMapper.incrementViewCount(serviceId);
    }
    
    @Override
    public boolean checkUserPermission(Long serviceId, Integer userId) {
        SkillService skillService = skillServiceMapper.selectById(serviceId);
        return skillService != null && skillService.getUserId().equals(userId);
    }
    
    /**
     * 保存服务图片
     */
    private void saveServiceImages(Long serviceId, List<String> imageUrls) {
        for (int i = 0; i < imageUrls.size(); i++) {
            SkillImage skillImage = new SkillImage();
            skillImage.setServiceId(serviceId);
            skillImage.setImageUrl(imageUrls.get(i));
            skillImage.setSortOrder(i + 1);
            skillImage.setCreatedTime(new Date());
            skillImageMapper.insert(skillImage);
        }
    }
    
    /**
     * 将Map转换为DTO
     */
    private SkillServiceDTO convertMapToDTO(Map<String, Object> map) {
        SkillServiceDTO dto = new SkillServiceDTO();
        
        // 基本信息
        dto.setServiceId(getLongValue(map, "service_id"));
        dto.setTitle((String) map.get("title"));
        dto.setDescription((String) map.get("description"));
        dto.setPrice(getBigDecimalValue(map, "price"));
        dto.setDuration(getIntegerValue(map, "duration"));
        dto.setServiceType(getIntegerValue(map, "service_type"));
        dto.setLocation((String) map.get("location"));
        dto.setMaxStudents(getIntegerValue(map, "max_students"));
        dto.setStatus(getIntegerValue(map, "status"));
        dto.setRatingAvg(getBigDecimalValue(map, "rating_avg"));
        dto.setRatingCount(getIntegerValue(map, "rating_count"));
        dto.setOrderCount(getIntegerValue(map, "order_count"));
        dto.setViewCount(getIntegerValue(map, "view_count"));
        dto.setCreatedTime((Date) map.get("created_time"));
        dto.setUpdatedTime((Date) map.get("updated_time"));
        
        // 分类信息
        dto.setCategoryId(getIntegerValue(map, "category_id"));
        dto.setCategoryName((String) map.get("category_name"));
        
        // 用户信息
        dto.setUserId(getIntegerValue(map, "user_id"));
        dto.setUserName((String) map.get("user_name"));
        dto.setUserAvatar((String) map.get("user_avatar"));
        dto.setUserAcademy((String) map.get("user_academy"));
        dto.setUserCreditScore(getIntegerValue(map, "user_credit_score"));
        
        // 处理标签
        String tagsJson = (String) map.get("tags");
        if (StringUtils.hasText(tagsJson)) {
            try {
                List<String> tags = objectMapper.readValue(tagsJson, List.class);
                dto.setTags(tags);
            } catch (JsonProcessingException e) {
                dto.setTags(new ArrayList<>());
            }
        } else {
            dto.setTags(new ArrayList<>());
        }
        
        return dto;
    }
    
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        if (value instanceof String) return Long.parseLong((String) value);
        return null;
    }
    
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Long) return ((Long) value).intValue();
        if (value instanceof String) return Integer.parseInt((String) value);
        return null;
    }
    
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof BigDecimal) return (BigDecimal) value;
        if (value instanceof Double) return BigDecimal.valueOf((Double) value);
        if (value instanceof String) return new BigDecimal((String) value);
        return null;
    }
}
