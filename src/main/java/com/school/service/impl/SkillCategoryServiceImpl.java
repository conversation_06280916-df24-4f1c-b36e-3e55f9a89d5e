package com.school.service.impl;

import com.school.entity.SkillCategory;
import com.school.mapper.SkillCategoryMapper;
import com.school.service.SkillCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 技能分类服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Service
public class SkillCategoryServiceImpl implements SkillCategoryService {
    
    @Autowired
    private SkillCategoryMapper skillCategoryMapper;
    
    @Override
    public List<SkillCategory> getEnabledCategories() {
        return skillCategoryMapper.selectEnabledCategories();
    }
    
    @Override
    public SkillCategory getCategoryById(Integer categoryId) {
        return skillCategoryMapper.selectById(categoryId);
    }
}
