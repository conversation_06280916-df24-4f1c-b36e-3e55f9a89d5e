package com.school.service.impl;

import com.school.service.VerificationCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现类
 * 
 * <AUTHOR>
 * @since 2024-07-24
 */
@Service
public class VerificationCodeServiceImpl implements VerificationCodeService {

    private static final long CODE_EXPIRE_MINUTES = 5;
    private static final String CODE_PREFIX = "verification_code:";
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Override
    public String generateCode(String email, String type) {
        // 生成6位随机数字验证码
        String code = generateRandomCode();
        
        // 存储验证码到Redis，设置过期时间为5分钟
        String key = buildKey(email, type);
        redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return code;
    }
    
    @Override
    public boolean verifyCode(String email, String code, String type) {
        if (email == null || code == null || type == null) {
            return false;
        }
        
        String key = buildKey(email, type);
        String storedCode = redisTemplate.opsForValue().get(key);
        
        return code.equals(storedCode);
    }
    
    @Override
    public void invalidateCode(String email, String type) {
        String key = buildKey(email, type);
        redisTemplate.delete(key);
    }
    
    /**
     * 生成6位随机数字验证码
     */
    private String generateRandomCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
    
    /**
     * 构建Redis中的键
     */
    private String buildKey(String email, String type) {
        return CODE_PREFIX + type + ":" + email;
    }
} 