package com.school.service.impl;

/**
 * 2023-10-03
 * 栋dong
 */


import com.school.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Service
public class EmailServiceImpl implements EmailService {

    private final JavaMailSender javaMailSender;
    private final TemplateEngine templateEngine;
    
    @Value("${spring.mail.username}")
    private String from;
    
    @Value("${app.name:校园技能共享平台}")
    private String appName;
    
    @Value("${app.email.templates.register-subject:注册验证码}")
    private String registerSubject;
    
    @Value("${app.email.templates.login-subject:登录验证码}")
    private String loginSubject;

    @Autowired
    public EmailServiceImpl(JavaMailSender javaMailSender, TemplateEngine templateEngine) {
        this.javaMailSender = javaMailSender;
        this.templateEngine = templateEngine;
    }

    @Override
    public boolean sendEmail(String to, String subject, String text) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);
            message.setFrom(from);
            javaMailSender.send(message);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    public boolean sendTemplateEmail(String to, String subject, String templateName, Map<String, Object> variables) {
        try {
            // 创建MimeMessage
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            
            // 设置收件人、主题、发件人
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setFrom(from);
            
            // 添加应用名称和当前年份
            if (variables == null) {
                variables = new HashMap<>();
            }
            variables.put("appName", appName);
            variables.put("year", LocalDate.now().getYear());
            
            // 使用Thymeleaf渲染模板
            Context context = new Context();
            variables.forEach(context::setVariable);
            
            String htmlContent = templateEngine.process("email/" + templateName, context);
            helper.setText(htmlContent, true);
            
            // 发送邮件
            javaMailSender.send(mimeMessage);
            return true;
        } catch (MessagingException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * @param email 用户邮箱
     * @param code  生成的六位随机数字验证码
     * @MethodName sendRegisterCode
     * @Description 为正在注册的用户发送一份注册验证码。
     * @Return
     * @Since 2023/10/29
     */
    @Async
    @Override
    public void sendRegisterCode(String email, String code) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("code", code);
        
        boolean useTemplate = sendTemplateEmail(email, registerSubject, "register-code", variables);
        
        // 如果模板发送失败，使用简单文本格式发送
        if (!useTemplate) {
            String text = "您好，您正在注册" + appName + "账号，验证码为：" + code + "，验证码有效期为5分钟，请勿泄露给他人。";
            sendEmail(email, registerSubject, text);
        }
    }
    
    /**
     * @param email 用户邮箱
     * @param code  生成的六位随机数字验证码
     * @MethodName sendLoginCode
     * @Description 为用户登录发送验证码
     * @Return
     * @Since 2024/07/24
     */
    @Async
    @Override
    public void sendLoginCode(String email, String code) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("code", code);
        
        boolean useTemplate = sendTemplateEmail(email, loginSubject, "login-code", variables);
        
        // 如果模板发送失败，使用简单文本格式发送
        if (!useTemplate) {
            String text = "您好，您正在登录" + appName + "，验证码为：" + code + "，验证码有效期为5分钟，请勿泄露给他人。";
            sendEmail(email, loginSubject, text);
        }
    }
}

