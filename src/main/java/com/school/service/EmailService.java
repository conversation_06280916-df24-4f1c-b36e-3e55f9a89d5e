package com.school.service;

import java.util.Map;

/**
 * 邮件服务接口
 * 
 * <AUTHOR>
 * @since 2024-07-24
 */
public interface EmailService {
    /**
     * 发送普通文本邮件
     * @param to 收件人
     * @param subject 主题
     * @param text 内容
     * @return 是否发送成功
     */
    boolean sendEmail(String to, String subject, String text);
    
    /**
     * 发送HTML模板邮件
     * @param to 收件人
     * @param subject 主题
     * @param templateName 模板名称（不含路径和后缀）
     * @param variables 模板变量
     * @return 是否发送成功
     */
    boolean sendTemplateEmail(String to, String subject, String templateName, Map<String, Object> variables);
    
    /**
     * 发送注册验证码
     * @param email 用户邮箱
     * @param code 验证码
     */
    void sendRegisterCode(String email, String code);
    
    /**
     * 发送登录验证码
     * @param email 用户邮箱  
     * @param code 验证码
     */
    void sendLoginCode(String email, String code);
}
