package com.school.service;

/**
 * 验证码服务接口
 * 
 * <AUTHOR>
 * @since 2024-07-24
 */
public interface VerificationCodeService {
    
    /**
     * 生成验证码
     * @param email 用户邮箱
     * @param type 验证码类型：register-注册，login-登录
     * @return 生成的验证码
     */
    String generateCode(String email, String type);
    
    /**
     * 验证验证码
     * @param email 用户邮箱
     * @param code 验证码
     * @param type 验证码类型：register-注册，login-登录
     * @return 是否验证通过
     */
    boolean verifyCode(String email, String code, String type);
    
    /**
     * 使验证码失效
     * @param email 用户邮箱
     * @param type 验证码类型：register-注册，login-登录
     */
    void invalidateCode(String email, String type);
} 