package com.school.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.school.dto.SkillPublishRequest;
import com.school.dto.SkillServiceDTO;
import com.school.entity.SkillService;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 技能服务业务接口
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
public interface SkillServiceService {
    
    /**
     * 分页查询技能服务列表
     */
    IPage<SkillServiceDTO> getSkillServicePage(
            Integer page, 
            Integer size, 
            String keyword, 
            Integer categoryId, 
            BigDecimal priceMin, 
            BigDecimal priceMax, 
            String sortBy
    );
    
    /**
     * 根据ID获取技能服务详情
     */
    SkillServiceDTO getSkillServiceDetail(Long serviceId);
    
    /**
     * 发布技能服务
     */
    Long publishSkillService(Integer userId, SkillPublishRequest request);
    
    /**
     * 更新技能服务
     */
    boolean updateSkillService(Long serviceId, Integer userId, SkillPublishRequest request);
    
    /**
     * 删除技能服务
     */
    boolean deleteSkillService(Long serviceId, Integer userId);
    
    /**
     * 获取用户发布的技能服务列表
     */
    IPage<SkillServiceDTO> getUserSkillServices(Integer userId, Integer page, Integer size, Integer status);
    
    /**
     * 增加浏览次数
     */
    void incrementViewCount(Long serviceId);
    
    /**
     * 检查用户是否有权限操作该服务
     */
    boolean checkUserPermission(Long serviceId, Integer userId);
}
