package com.school.service;

import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.school.entity.Users;
import com.school.entity.dto.IdentityVerifyDto;
import com.school.entity.vo.UserInfoVo;
import com.school.entity.vo.UserVo;
import com.school.entity.vo.UserListVo;
import com.school.entity.vo.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * (User)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-12 21:36:44
 */
public interface UserService extends IService<Users> {

    Integer login(Users user);
    SaResult importUsersFromExcelAndSave(MultipartFile file) throws Exception;

    UserInfoVo getUserInfo(int userId);

    /**
     * 模糊查询用户
     * @param query 查询条件（学号或姓名或组别）
     * @return 用户列表
     */
    List<UserVo> searchUsers(String query);

    /**
     * 批量获取用户基本信息
     * @param userIds 用户ID列表
     * @return 用户基本信息列表
     */
    List<Map<String, Object>> batchGetUserBasicInfo(List<String> userIds);

    /**
     * 分页查询用户列表
     * @param page 页码
     * @param pageSize 每页大小
     * @param query 查询条件
     * @param status 用户状态
     * @return 分页结果
     */
    PageResult<UserListVo> getUserList(Integer page, Integer pageSize, String query, Integer status);

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 是否更新成功
     */
    boolean updateUserInfo(UserListVo user);

    /**
     * 检查用户是否是首次登录
     * @param userId 用户ID
     * @return 是否首次登录
     */
    boolean isFirstLogin(Integer userId);

    /**
     * 更新用户首次登录状态
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean updateFirstLoginStatus(Integer userId);

    /**
     * 更新用户密码
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否更新成功
     */
    boolean updatePassword(Integer userId, String oldPassword, String newPassword);

    /**
     * 获取所有角色类型
     * @return 角色类型列表
     */
    List<String> getAllRoles();
    
    /**
     * 发送邮箱验证码
     * @param email 用户邮箱
     * @return 操作结果
     */
    boolean sendEmailVerificationCode(String email);
    
    /**
     * 使用邮箱验证码登录
     * @param email 用户邮箱
     * @param code 验证码
     * @return 用户ID，登录失败则返回null
     */
    Integer loginWithCode(String email, String code);
    
    /**
     * 用户注册
     * @param user 用户信息
     * @param code 验证码
     * @return 注册成功返回用户ID，失败返回null
     */
    Integer register(Users user, String code);
    
    /**
     * 检查邮箱是否已经存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean isEmailExist(String email);

    /**
     * 用户身份验证
     * @param identityVerifyDto 身份验证信息
     * @return 是否提交成功
     */
    boolean verifyIdentity(IdentityVerifyDto identityVerifyDto, Integer userId);
    
    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 新状态
     * @param reason 状态变更原因
     * @return 是否更新成功
     */
    boolean updateUserStatus(Integer userId, Integer status, String reason);

    /**
     * 获取用户认证状态
     * @param userId 用户ID
     * @return 用户认证状态码（1-正常/已认证，2-待审核，5-待上传证件）
     */
    Integer getVerificationStatus(Integer userId);
}

