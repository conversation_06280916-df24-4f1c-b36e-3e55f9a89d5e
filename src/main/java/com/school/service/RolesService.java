package com.school.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.school.entity.Roles;

import java.util.List;

/**
 * 角色表(Roles)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-14 18:12:31
 */
public interface RolesService extends IService<Roles> {
    
    /**
     * 获取角色列表
     * @return 角色列表
     */
    List<Roles> getRoleList();

    /**
     * 批量为用户添加角色
     * @param userIds 用户ID列表
     * @param roleId 角色ID
     * @return 是否添加成功
     */
    boolean addUsersToRole(List<Integer> userIds, Integer roleId);

    /**
     * 创建新角色
     * @param role 角色信息
     * @return 是否创建成功
     */
    boolean createRole(Roles role);

    /**
     * 更新角色信息
     * @param role 角色信息
     * @return 是否更新成功
     */
    boolean updateRole(Roles role);

    /**
     * 删除角色
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean deleteRole(Integer roleId);

    /**
     * 从角色中移除指定用户
     * @param userIds 用户ID列表
     * @param roleId 角色ID
     * @return 是否移除成功
     */
    boolean removeUsersFromRole(List<Integer> userIds, Integer roleId);

    /**
     * 删除用户的指定角色
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否删除成功
     */
    boolean deleteUserRoles(Integer userId, List<Integer> roleIds);

    /**
     * 获取角色下的用户列表
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<Object> getRoleUsers(Integer roleId);
}

