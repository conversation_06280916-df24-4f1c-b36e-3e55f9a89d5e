package com.school.controller.user;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.school.annotation.OperationLog;
import com.school.entity.Users;
import com.school.entity.dto.*;
import com.school.entity.vo.UserInfoVo;
import com.school.entity.vo.UserListVo;
import com.school.enums.UserStatus;
import com.school.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制层 - 校园技能共享平台
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@RestController
@RequestMapping("user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @OperationLog(operationModule = "用户管理", operationType = "登录", operationDesc = "用户登录")
    public SaResult login(@RequestBody LoginDto loginDto) {
        try {
            // 将DTO转换为Users对象
            Users user = new Users();
            user.setEmail(loginDto.getEmail());
            user.setPassword(loginDto.getPassword());
            
            Integer userId = userService.login(user);
            if (userId != null) {
                StpUtil.login(userId);
                // 检查是否首次登录
                boolean isFirstLogin = userService.isFirstLogin(userId);

                Map<String, Object> result = new HashMap<>();
                result.put("tokenInfo", StpUtil.getTokenInfo());
                result.put("isFirstLogin", isFirstLogin);
                result.put("userInfo", userService.getUserInfo(userId));

                return SaResult.data(result);
            }
            return SaResult.error("登录失败，邮箱或密码错误");
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 发送邮箱验证码
     */
    @PostMapping("/send-email-code")
    @OperationLog(operationModule = "用户管理", operationType = "发送验证码", operationDesc = "发送邮箱验证码")
    public SaResult sendEmailCode(@RequestBody EmailCodeDto emailCodeDto) {
        String email = emailCodeDto.getEmail();
        if (email == null || email.trim().isEmpty()) {
            return SaResult.error("邮箱不能为空");
        }

        boolean sent = userService.sendEmailVerificationCode(email);
        if (sent) {
            return SaResult.ok("验证码已发送，请查收邮件");
        } else {
            return SaResult.error("验证码发送失败，请稍后重试");
        }
    }

    /**
     * 邮箱验证码登录
     */
    @PostMapping("/login-with-code")
    @OperationLog(operationModule = "用户管理", operationType = "验证码登录", operationDesc = "使用邮箱验证码登录")
    public SaResult loginWithCode(@RequestBody LoginCodeDto loginCodeDto) {
        try {
            String email = loginCodeDto.getEmail();
            String code = loginCodeDto.getCode();

            if (email == null || email.trim().isEmpty()) {
                return SaResult.error("邮箱不能为空");
            }
            if (code == null || code.trim().isEmpty()) {
                return SaResult.error("验证码不能为空");
            }

            Integer userId = userService.loginWithCode(email, code);
            if (userId != null) {
                StpUtil.login(userId);
                // 检查是否首次登录
                boolean isFirstLogin = userService.isFirstLogin(userId);

                Map<String, Object> result = new HashMap<>();
                result.put("tokenInfo", StpUtil.getTokenInfo());
                result.put("isFirstLogin", isFirstLogin);
                result.put("userInfo", userService.getUserInfo(userId));

                return SaResult.data(result);
            }
            return SaResult.error("登录失败，邮箱或验证码错误");
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @OperationLog(operationModule = "用户管理", operationType = "注册", operationDesc = "用户注册")
    public SaResult register(@RequestBody RegisterDto registerDto) {
        try {
            // 提取注册信息并转换为Users对象
            Users user = new Users();
            user.setName(registerDto.getName());
            user.setEmail(registerDto.getEmail());
            user.setPassword(registerDto.getPassword());
            user.setStuId(registerDto.getStuId());
            user.setCardId(registerDto.getCardId());
            user.setAcademy(registerDto.getAcademy());

            String code = registerDto.getVerificationCode();
            String confirmPassword = registerDto.getConfirmPassword();

            if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
                return SaResult.error("邮箱不能为空");
            }
            if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
                return SaResult.error("密码不能为空");
            }
            // 学号、一卡通号和学院改为可选字段，后续可在身份验证中填写
            if (!user.getPassword().equals(confirmPassword)) {
                return SaResult.error("两次输入的密码不一致");
            }
            if (code == null || code.trim().isEmpty()) {
                return SaResult.error("验证码不能为空");
            }

            // 调用注册服务
            Integer userId = userService.register(user, code);
            if (userId != null) {
                return SaResult.ok("注册成功");
            }
            return SaResult.error("注册失败");
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/check-login")
    @OperationLog(operationModule = "用户管理", operationType = "登录检查", operationDesc = "检查登录状态")
    public SaResult checkLogin() {
        StpUtil.checkLogin();
        return SaResult.data(StpUtil.getTokenInfo());
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @OperationLog(operationModule = "用户管理", operationType = "登出", operationDesc = "用户登出")
    public SaResult logout() {
        StpUtil.logout(StpUtil.getLoginId());
        return SaResult.ok("退出成功");
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    @OperationLog(operationModule = "用户管理", operationType = "获取用户信息", operationDesc = "获取当前用户信息")
    public SaResult getUserInfo() {
        Integer userId = StpUtil.getLoginIdAsInt();
        UserInfoVo userInfo = userService.getUserInfo(userId);
        return SaResult.data(userInfo);
    }

    /**
     * 修改密码
     */
    @PutMapping("/password")
    @OperationLog(operationModule = "用户管理", operationType = "修改密码", operationDesc = "用户修改密码")
    public SaResult updatePassword(@RequestBody PasswordUpdateDto passwordDto) {
        try {
            Integer userId = StpUtil.getLoginIdAsInt();
            boolean success = userService.updatePassword(userId, passwordDto.getOldPassword(), passwordDto.getNewPassword());
            if (success) {
                return SaResult.ok("密码修改成功");
            } else {
                return SaResult.error("原密码错误");
            }
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    @OperationLog(operationModule = "用户管理", operationType = "搜索用户", operationDesc = "搜索用户")
    public SaResult searchUsers(@RequestParam String query) {
        try {
            return SaResult.data(userService.searchUsers(query));
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    // ============ 管理员功能 ============

    /**
     * 获取用户列表（管理员）
     */
    @GetMapping("/list")
    @OperationLog(operationModule = "用户管理", operationType = "查询用户列表", operationDesc = "管理员查询用户列表")
    public SaResult getUserList(@RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "20") Integer size, @RequestParam(required = false) String query, @RequestParam(required = false) Integer status) {
        try {
            // 检查管理员权限
            StpUtil.checkPermission("user:admin");
            return SaResult.data(userService.getUserList(page, size, query, status));
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 更新用户信息（管理员/用户自己）
     */
    @PostMapping("/update")
    @OperationLog(operationModule = "用户管理", operationType = "用户数据更新", operationDesc = "用户数据更新")
    public SaResult update(@RequestBody UserListVo user) {
        if (user.getUserId() == null) {
            return SaResult.error("用户ID不能为空");
        }

        boolean isFirstLogin = userService.isFirstLogin(user.getUserId());

        // 只有超级管理员可以更新用户角色
        if (StpUtil.hasRole("superAdmin")) {
            try {
                boolean success = userService.updateUserInfo(user);
                if (success) {
                    // 如果是首次登录用户信息更新，则更新首次登录状态
                    if (isFirstLogin) {
                        userService.updateFirstLoginStatus(user.getUserId());
                    }
                    return SaResult.ok("更新成功");
                } else {
                    return SaResult.error("更新失败");
                }
            } catch (Exception e) {
                return SaResult.error(e.getMessage());
            }
        } else {
            // 如果不是超级管理员，只能修改自己的基本信息，不能修改角色
            if (!user.getUserId().equals(StpUtil.getLoginIdAsInt())) {
                return SaResult.error("权限不足，您只能修改自己的信息");
            }
            // 清除角色信息，防止非管理员修改角色
            user.setRoles(null);
            try {
                boolean success = userService.updateUserInfo(user);
                if (success) {
                    // 如果是首次登录用户信息更新，则更新首次登录状态
                    if (isFirstLogin) {
                        userService.updateFirstLoginStatus(user.getUserId());
                    }
                    return SaResult.ok("更新成功");
                } else {
                    return SaResult.error("更新失败");
                }
            } catch (Exception e) {
                return SaResult.error(e.getMessage());
            }
        }
    }

    /**
     * 用户身份验证
     */
    @PostMapping("/verify-identity")
    @OperationLog(operationModule = "用户管理", operationType = "身份验证", operationDesc = "用户提交身份验证信息")
    public SaResult verifyIdentity(@RequestBody IdentityVerifyDto identityVerifyDto) {
        try {
            // 获取当前登录用户ID
            Integer userId = StpUtil.getLoginIdAsInt();
            
            // 调用身份验证服务
            boolean success = userService.verifyIdentity(identityVerifyDto, userId);
            
            if (success) {
                return SaResult.ok("身份验证信息提交成功，请等待审核");
            } else {
                return SaResult.error("身份验证信息提交失败");
            }
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 获取用户身份验证状态
     */
    @GetMapping("/verify-status")
    @OperationLog(operationModule = "用户管理", operationType = "查询", operationDesc = "查询用户认证状态")
    public SaResult getVerificationStatus() {
        try {
            // 获取当前登录用户ID
            Integer userId = StpUtil.getLoginIdAsInt();
            
            // 调用身份验证状态查询服务
            Integer status = userService.getVerificationStatus(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("status", status);
            result.put("statusText", UserStatus.getByCode(status).getDescription());
            
            return SaResult.data(result);
        } catch (Exception e) {
            return SaResult.error(e.getMessage());
        }
    }
}


