package com.school.controller.user;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.util.SaResult;
import com.school.annotation.OperationLog;
import com.school.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 2025/2/18
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/user")
@SaCheckRole(value="superAdmin")
public class UserAdminController {
    @Autowired
    private UserService userService;

    /**
     * 获取所有角色类型
     * @return 角色类型列表
     */
    @GetMapping("/roles")
    @OperationLog(
            operationModule = "用户管理",
            operationType = "获取角色类型",
            operationDesc = "获取系统中所有可用的角色类型"
    )
    public SaResult getAllRoles() {
        return SaResult.data(userService.getAllRoles());
    }

    /**
     * 分页查询用户列表
     * @param page 页码
     * @param pageSize 每页大小
     * @param query 查询条件
     * @param status 状态
     * @return 用户列表
     */
    @GetMapping("/list")
    @OperationLog(
            operationModule = "用户管理",
            operationType = "用户列表分页获取",
            operationDesc = "用户列表分页获取"
    )
    public SaResult getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String query,
            @RequestParam(defaultValue = "1") Integer status) {
//        System.out.println(123);
        return SaResult.data(userService.getUserList(page, pageSize, query, status));
    }
    
    /**
     * @param file 学生名单
     * @return 一个文件，比上面的新增密码
     * @throws Exception
     */
    @PostMapping("/caeateUserByExcel")
//    @OperationLog(
//            operationModule = "用户管理",
//            operationType = "用户创建",
//            operationDesc = "根据上传的文件创建用户"
//    )
    public SaResult createUserByExcel(MultipartFile file) throws Exception {
        return userService.importUsersFromExcelAndSave(file);
//        return SaResult.ok();
    }

    /**
     * 模糊查询用户
     * @param query 查询条件（学号或姓名或组别）
     * @return 用户列表
     */
    @GetMapping("/search")
    @OperationLog(
            operationModule = "用户管理",
            operationType = "用户信息模糊查询",
            operationDesc = "根据学号或姓名查询到符合要求的全部数据"
    )
    public SaResult searchUsers(@RequestParam String query) {
        return SaResult.data(userService.searchUsers(query));
    }

    /**
     * 批量查询用户基本信息
     * @param userIds 用户ID列表
     * @return 用户基本信息（ID、姓名、学号）列表
     */
    @PostMapping("/batch-info")
    @OperationLog(
            operationModule = "用户管理",
            operationType = "批量查询用户基本信息",
            operationDesc = "根据用户ID查询到对应的学生姓名学号"
    )
    public SaResult batchGetUserInfo(@RequestBody List<String> userIds) {
        return SaResult.data(userService.batchGetUserBasicInfo(userIds));
    }
}
