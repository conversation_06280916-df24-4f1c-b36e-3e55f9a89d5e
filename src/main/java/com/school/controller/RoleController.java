package com.school.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.util.SaResult;
import com.school.entity.Roles;
import com.school.service.impl.RolesServiceImpl;
import com.school.annotation.OperationLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/roles")
@SaCheckRole("superAdmin")
public class RoleController {

    @Autowired
    private RolesServiceImpl rolesService;

    /**
     * 获取角色列表
     */
    @GetMapping
    @OperationLog(
        operationModule = "角色管理",
        operationType = "查询",
        operationDesc = "获取角色列表"
    )
    public SaResult getRoleList() {
        List<Roles> roles = rolesService.getRoleList();
        return SaResult.data(roles);
    }

    /**
     * 创建新角色
     */
    @PostMapping
    @OperationLog(
        operationModule = "角色管理",
        operationType = "新增",
        operationDesc = "创建新角色"
    )
    public SaResult createRole(@RequestBody Roles role) {
        boolean success = rolesService.createRole(role);
        return success ? SaResult.ok() : SaResult.error("创建角色失败");
    }

    /**
     * 更新角色信息
     */
    @PutMapping("/{roleId}")
    @OperationLog(
        operationModule = "角色管理",
        operationType = "更新",
        operationDesc = "更新角色信息"
    )
    public SaResult updateRole(@PathVariable Integer roleId, @RequestBody Roles role) {
        role.setRoleId(roleId);  // 确保使用路径中的roleId
        boolean success = rolesService.updateRole(role);
        return success ? SaResult.ok() : SaResult.error("更新角色失败");
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleId}")
    @OperationLog(
        operationModule = "角色管理",
        operationType = "删除",
        operationDesc = "删除角色"
    )
    public SaResult deleteRole(@PathVariable Integer roleId) {
        boolean success = rolesService.deleteRole(roleId);
        return success ? SaResult.ok() : SaResult.error("删除角色失败");
    }

    /**
     * 获取角色下的用户列表
     */
    @GetMapping("/{roleId}/users")
    @OperationLog(
        operationModule = "角色管理",
        operationType = "查询",
        operationDesc = "获取角色下的用户列表"
    )
    public SaResult getRoleUsers(@PathVariable Integer roleId) {
        List<Object> users = rolesService.getRoleUsers(roleId);
        return SaResult.data(users);
    }

    /**
     * 批量为用户添加角色
     */
    @PostMapping("/{roleId}/users")
    @OperationLog(
        operationModule = "角色管理",
        operationType = "新增",
        operationDesc = "批量为用户添加角色"
    )
    public SaResult addUsersToRole(@PathVariable Integer roleId, @RequestBody List<Integer> userIds) {
        boolean success = rolesService.addUsersToRole(userIds, roleId);
        return success ? SaResult.ok() : SaResult.error("添加用户到角色失败");
    }

    /**
     * 从角色中移除指定用户
     */
    @DeleteMapping("/{roleId}/users")
    @OperationLog(
        operationModule = "角色管理",
        operationType = "删除",
        operationDesc = "从角色中移除指定用户"
    )
    public SaResult removeUsersFromRole(@PathVariable Integer roleId, @RequestBody List<Integer> userIds) {
        boolean success = rolesService.removeUsersFromRole(userIds, roleId);
        return success ? SaResult.ok() : SaResult.error("从角色中移除用户失败");
    }

    /**
     * 删除用户的指定角色
     */
    @DeleteMapping("/users/{userId}/roles")
    @OperationLog(
        operationModule = "角色管理",
        operationType = "删除",
        operationDesc = "删除用户的指定角色"
    )
    public SaResult deleteUserRoles(@PathVariable Integer userId, @RequestBody List<Integer> roleIds) {
        boolean success = rolesService.deleteUserRoles(userId, roleIds);
        return success ? SaResult.ok() : SaResult.error("删除用户角色失败");
    }
} 