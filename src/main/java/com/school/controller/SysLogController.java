package com.school.controller;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.school.entity.SysLog;
import com.school.mapper.SysLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统日志控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/logs")
@SaCheckRole("superAdmin")
public class SysLogController {
    
    @Autowired
    private SysLogMapper sysLogMapper;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 分页查询系统日志
     */
    @GetMapping
    public SaResult getSystemLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String operationModule,
            @RequestParam(required = false) String responseCode
    ) {
        LambdaQueryWrapper<SysLog> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (startTime != null && !startTime.isEmpty() && endTime != null && !endTime.isEmpty()) {
            wrapper.between(SysLog::getOperationTime, 
                LocalDateTime.parse(startTime, DATE_TIME_FORMATTER), 
                LocalDateTime.parse(endTime, DATE_TIME_FORMATTER));
        }
        if (userId != null && !userId.isEmpty()) {
            wrapper.eq(SysLog::getUserId, Long.parseLong(userId));
        }
        if (username != null && !username.isEmpty()) {
            wrapper.like(SysLog::getUsername, username);
        }
        if (operationType != null && !operationType.isEmpty()) {
            wrapper.eq(SysLog::getOperationType, operationType);
        }
        if (operationModule != null && !operationModule.isEmpty()) {
            wrapper.eq(SysLog::getOperationModule, operationModule);
        }
        if (responseCode != null && !responseCode.isEmpty()) {
            wrapper.eq(SysLog::getResponseCode, Integer.parseInt(responseCode));
        }
        
        // 按时间倒序排序
        wrapper.orderByDesc(SysLog::getOperationTime);
        
        // 执行分页查询
        Page<SysLog> pageResult = sysLogMapper.selectPage(
            new Page<>(page, pageSize), 
            wrapper
        );
        
        return SaResult.data(pageResult);
    }
    
    /**
     * 获取所有操作类型列表
     */
    @GetMapping("/operation-types")
    public SaResult getOperationTypes() {
        List<String> types = sysLogMapper.selectList(null)
            .stream()
            .map(SysLog::getOperationType)
            .distinct()
            .collect(Collectors.toList());
        return SaResult.data(types);
    }
    
    /**
     * 获取所有操作模块列表
     */
    @GetMapping("/operation-modules")
    public SaResult getOperationModules() {
        List<String> modules = sysLogMapper.selectList(null)
            .stream()
            .map(SysLog::getOperationModule)
            .distinct()
            .collect(Collectors.toList());
        return SaResult.data(modules);
    }
    
    /**
     * 获取所有响应状态码列表
     */
    @GetMapping("/response-codes")
    public SaResult getResponseCodes() {
        List<Integer> codes = sysLogMapper.selectList(null)
            .stream()
            .map(SysLog::getResponseCode)
            .distinct()
            .collect(Collectors.toList());
        return SaResult.data(codes);
    }
    
    /**
     * 获取日志统计数据
     */
    @GetMapping("/statistics")
    public SaResult getLogStatistics() {
        // 获取总请求数
        long totalRequests = sysLogMapper.selectCount(null);
        
        // 获取24小时内的请求数
        LambdaQueryWrapper<SysLog> last24HoursWrapper = new LambdaQueryWrapper<>();
        last24HoursWrapper.ge(SysLog::getOperationTime, LocalDateTime.now().minusHours(24));
        long last24HoursRequests = sysLogMapper.selectCount(last24HoursWrapper);
        
        // 获取响应状态码为200的请求数
        LambdaQueryWrapper<SysLog> successWrapper = new LambdaQueryWrapper<>();
        successWrapper.eq(SysLog::getResponseCode, 200);
        long successRequests = sysLogMapper.selectCount(successWrapper);
        
        // 计算其他状态码的请求数
        long otherRequests = totalRequests - successRequests;
        
        return SaResult.data(Map.of(
            "totalRequests", totalRequests,
            "last24HoursRequests", last24HoursRequests,
            "successRequests", successRequests,
            "otherRequests", otherRequests
        ));
    }
}
