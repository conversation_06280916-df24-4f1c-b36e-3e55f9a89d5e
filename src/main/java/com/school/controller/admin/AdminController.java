package com.school.controller.admin;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.school.annotation.OperationLog;
import com.school.entity.Audits;
import com.school.entity.Users;
import com.school.service.AuditService;
import com.school.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {

    private final AuditService auditService;
    private final UserService userService;

//    @GetMapping("/dashboard")
//    @SaCheckRole("admin")
//    @OperationLog(operationModule = "管理", operationType = "查询", operationDesc = "查询控制台数据")
//    public SaResult getDashboardData(@RequestParam(defaultValue = "7") String timeRange) {
//        try {
//            // TODO: 实现控制台数据查询逻辑
//            return SaResult.ok("控制台数据查询成功");
//        } catch (Exception e) {
//            return SaResult.error("控制台数据查询失败: " + e.getMessage());
//        }
//    }

    @GetMapping("/audits")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "查询", operationDesc = "查询审核列表")
    public SaResult getAuditList(@RequestParam(defaultValue = "1") Integer page,
                                @RequestParam(defaultValue = "10") Integer pageSize,
                                @RequestParam(required = false) String type,
                                @RequestParam(required = false) Integer status,
                                @RequestParam(required = false) String keyword,
                                @RequestParam(required = false) String startTime,
                                @RequestParam(required = false) String endTime) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<Audits> queryWrapper = new LambdaQueryWrapper<>();
            
            // 如果指定了类型，仅查询该类型
            if (StringUtils.hasText(type)) {
                queryWrapper.eq(Audits::getTargetType, type);
            }
            
            // 如果指定了状态，仅查询该状态
            if (status != null) {
                queryWrapper.eq(Audits::getStatus, status);
            }
            
            // 关键词搜索（在目标名称和审核内容中）
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> 
                    wrapper.like(Audits::getTargetName, keyword)
                           .or()
                           .like(Audits::getContent, keyword)
                );
            }
            
            // 时间范围过滤
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            if (StringUtils.hasText(startTime)) {
                LocalDateTime start = LocalDateTime.parse(startTime, formatter);
                queryWrapper.ge(Audits::getSubmitTime, start);
            }
            if (StringUtils.hasText(endTime)) {
                LocalDateTime end = LocalDateTime.parse(endTime, formatter);
                queryWrapper.le(Audits::getSubmitTime, end);
            }
            
            // 按提交时间倒序排序（新的在前）
            queryWrapper.orderByDesc(Audits::getSubmitTime);
            
            // 分页查询
            Page<Audits> pageResult = auditService.page(new Page<>(page, pageSize), queryWrapper);
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pageResult.getRecords());
            result.put("total", pageResult.getTotal());
            result.put("pages", pageResult.getPages());
            result.put("current", pageResult.getCurrent());
            result.put("size", pageResult.getSize());
            
            return SaResult.data(result);
        } catch (Exception e) {
            return SaResult.error("审核列表查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/audit/{id}")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "审核", operationDesc = "审核用户身份认证")
    public SaResult auditItem(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        try {
            Integer status = (Integer) params.get("status");
            String reason = (String) params.get("reason");
            
            if (status == null) {
                return SaResult.error("审核状态不能为空");
            }
            
            // 获取当前管理员信息
            Long auditorId = StpUtil.getLoginIdAsLong();
            Users auditor = userService.getById(auditorId);
            if (auditor == null) {
                return SaResult.error("获取审核员信息失败");
            }
            
            // 查询审核记录
            Audits audit = auditService.getById(id);
            if (audit == null) {
                return SaResult.error("审核记录不存在");
            }
            
            // 更新审核信息
            audit.setStatus(status);
            audit.setReason(reason);
            audit.setAuditTime(LocalDateTime.now());
            audit.setAuditorId(auditorId.intValue());
            audit.setAuditorName(auditor.getName());
            
            boolean success = auditService.updateById(audit);
            if (!success) {
                return SaResult.error("审核操作失败");
            }
            
            // 如果是用户身份认证的审核
            if ("user".equals(audit.getTargetType())) {
                Users user = userService.getById(audit.getTargetId());
                if (user != null) {
                    if (status == 1) {
                        // 审核通过，更新用户状态为已认证（1-正常）
                        user.setStatus(1);
                        user.setStatusReason("身份认证审核通过");
                    } else if (status == 2) {
                        // 审核拒绝，用户状态设为待上传证件（5-待上传证件）
                        user.setStatus(5);
                        user.setStatusReason(reason != null ? reason : "身份认证审核不通过");
                    }
                    user.setStatusUpdateTime(new Date());
                    userService.updateById(user);
                }
            }
            
            return SaResult.ok("审核操作成功");
        } catch (Exception e) {
            return SaResult.error("审核操作失败: " + e.getMessage());
        }
    }

    @GetMapping("/users")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "查询", operationDesc = "查询用户管理列表")
    public SaResult getAdminUserList(@RequestParam(defaultValue = "1") Integer page,
                                    @RequestParam(defaultValue = "10") Integer size,
                                    @RequestParam(required = false) String role,
                                    @RequestParam(required = false) Integer status,
                                    @RequestParam(required = false) String keyword) {
        try {
            // TODO: 实现用户管理列表查询逻辑
            return SaResult.ok("用户管理列表查询成功");
        } catch (Exception e) {
            return SaResult.error("用户管理列表查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/skills")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "查询", operationDesc = "查询技能服务管理列表")
    public SaResult getAdminSkillList(@RequestParam(defaultValue = "1") Integer page,
                                     @RequestParam(defaultValue = "10") Integer size,
                                     @RequestParam(required = false) Integer categoryId,
                                     @RequestParam(required = false) Integer status,
                                     @RequestParam(required = false) String keyword) {
        try {
            // TODO: 实现技能服务管理列表查询逻辑
            return SaResult.ok("技能服务管理列表查询成功");
        } catch (Exception e) {
            return SaResult.error("技能服务管理列表查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/user/{userId}/status")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "修改", operationDesc = "修改用户状态")
    public SaResult changeUserStatus(@PathVariable Long userId, @RequestBody Map<String, Object> params) {
        try {
            Integer status = (Integer) params.get("status");
            String reason = (String) params.get("reason");
            
            if (status == null) {
                return SaResult.error("用户状态不能为空");
            }
            
            Long operatorId = StpUtil.getLoginIdAsLong();
            Users operator = userService.getById(operatorId);
            if (operator == null) {
                return SaResult.error("获取操作员信息失败");
            }
            
            // 使用service层方法更新用户状态
            boolean success = userService.updateUserStatus(userId.intValue(), status, reason);
            if (!success) {
                return SaResult.error("用户状态修改失败");
            }
            
            // 如果是禁用或解禁操作，记录审核信息
            if (status == 3 || (status == 1 && userService.getById(userId).getStatus() != 1)) {
                Audits audit = new Audits();
                audit.setTargetId(userId);
                audit.setTargetType("user");
                audit.setTargetName(userService.getById(userId).getName());
                audit.setStatus(1); // 自动审核通过
                audit.setSubmitTime(LocalDateTime.now());
                audit.setAuditTime(LocalDateTime.now());
                audit.setAuditorId(operatorId.intValue());
                audit.setAuditorName(operator.getName());
                audit.setReason(reason);
                audit.setContent(status == 3 ? "管理员禁用用户账号" : "管理员解禁用户账号");
                
                auditService.save(audit);
            }
            
            return SaResult.ok("用户状态修改成功");
        } catch (Exception e) {
            return SaResult.error("用户状态修改失败: " + e.getMessage());
        }
    }

    @PostMapping("/skill/{skillId}/status")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "修改", operationDesc = "修改技能服务状态")
    public SaResult changeSkillStatus(@PathVariable Long skillId, @RequestBody Map<String, Object> params) {
        try {
            Integer status = (Integer) params.get("status");
            String reason = (String) params.get("reason");
            
            if (status == null) {
                return SaResult.error("技能服务状态不能为空");
            }
            
            Long operatorId = StpUtil.getLoginIdAsLong();
            // TODO: 实现技能服务状态修改逻辑
            
            return SaResult.ok("技能服务状态修改成功");
        } catch (Exception e) {
            return SaResult.error("技能服务状态修改失败: " + e.getMessage());
        }
    }

    @GetMapping("/logs")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "查询", operationDesc = "查询系统操作日志")
    public SaResult getOperationLogs(@RequestParam(defaultValue = "1") Integer page,
                                    @RequestParam(defaultValue = "10") Integer size,
                                    @RequestParam(required = false) Long operatorId,
                                    @RequestParam(required = false) String operationType,
                                    @RequestParam(required = false) String startTime,
                                    @RequestParam(required = false) String endTime) {
        try {
            // TODO: 实现系统操作日志查询逻辑
            return SaResult.ok("系统操作日志查询成功");
        } catch (Exception e) {
            return SaResult.error("系统操作日志查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/audit/{id}/detail")
    @SaCheckRole("superAdmin")
    @OperationLog(operationModule = "管理", operationType = "查询", operationDesc = "查询审核详情")
    public SaResult getAuditDetail(@PathVariable Long id) {
        try {
            // 查询审核记录
            Audits audit = auditService.getById(id);
            if (audit == null) {
                return SaResult.error("审核记录不存在");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("audit", audit);
            
            // 根据审核类型获取不同的详情信息
            if ("user".equals(audit.getTargetType())) {
                // 获取用户信息
                Users user = userService.getById(audit.getTargetId());
                if (user != null) {
                    Map<String, Object> userInfo = new HashMap<>();
                    // 只返回必要的用户信息，不包括敏感信息
                    userInfo.put("userId", user.getUserId());
                    userInfo.put("name", user.getName());
                    userInfo.put("stuId", user.getStuId());
                    userInfo.put("cardId", user.getCardId());
                    userInfo.put("academy", user.getAcademy());
                    userInfo.put("email", user.getEmail());
                    userInfo.put("avatar", user.getAvatar());
                    userInfo.put("cardPhoto", user.getCardPhoto());
                    userInfo.put("userType", user.getUserType());
                    userInfo.put("status", user.getStatus());
                    userInfo.put("statusReason", user.getStatusReason());
                    
                    result.put("targetDetail", userInfo);
                }
            } else if ("skill".equals(audit.getTargetType())) {
                // TODO: 技能服务详情查询逻辑
                // 这里可以调用技能服务相关的Service方法获取详情
                result.put("targetDetail", null);
            }
            
            return SaResult.data(result);
        } catch (Exception e) {
            return SaResult.error("获取审核详情失败: " + e.getMessage());
        }
    }
} 