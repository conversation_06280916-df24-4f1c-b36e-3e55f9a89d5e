package com.school.controller.skill;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.school.annotation.OperationLog;
import com.school.dto.SkillPublishRequest;
import com.school.dto.SkillServiceDTO;
import com.school.entity.SkillCategory;
import com.school.service.SkillCategoryService;
import com.school.service.SkillServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 技能服务控制层
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@RestController
@RequestMapping("skill")
public class SkillController {

    @Autowired
    private SkillCategoryService skillCategoryService;

    @Autowired
    private SkillServiceService skillServiceService;

    /**
     * 获取技能分类列表
     */
    @GetMapping("/categories")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "查询分类",
            operationDesc = "获取技能分类列表"
    )
    public SaResult getCategories() {
        try {
            List<SkillCategory> categories = skillCategoryService.getEnabledCategories();
            return SaResult.ok("获取分类成功").setData(categories);
        } catch (Exception e) {
            return SaResult.error("获取分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能服务列表
     */
    @GetMapping("/list")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "查询列表",
            operationDesc = "获取技能服务列表"
    )
    public SaResult getSkillList(@RequestParam(defaultValue = "1") Integer page,
                               @RequestParam(defaultValue = "20") Integer size,
                               @RequestParam(required = false) Integer categoryId,
                               @RequestParam(required = false) String keyword,
                               @RequestParam(required = false) String sortBy,
                               @RequestParam(required = false) BigDecimal priceMin,
                               @RequestParam(required = false) BigDecimal priceMax) {
        try {
            IPage<SkillServiceDTO> result = skillServiceService.getSkillServicePage(
                    page, size, keyword, categoryId, priceMin, priceMax, sortBy);

            Map<String, Object> data = new HashMap<>();
            data.put("records", result.getRecords());
            data.put("total", result.getTotal());
            data.put("size", result.getSize());
            data.put("current", result.getCurrent());
            data.put("pages", result.getPages());

            return SaResult.ok("查询成功").setData(data);
        } catch (Exception e) {
            return SaResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能服务详情
     */
    @GetMapping("/{serviceId}")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "查看详情",
            operationDesc = "查看技能服务详情"
    )
    public SaResult getSkillDetail(@PathVariable Long serviceId) {
        try {
            SkillServiceDTO skillDetail = skillServiceService.getSkillServiceDetail(serviceId);
            if (skillDetail == null) {
                return SaResult.error("技能服务不存在");
            }
            return SaResult.ok("查询成功").setData(skillDetail);
        } catch (Exception e) {
            return SaResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 发布技能服务
     */
    @PostMapping("/publish")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "发布服务",
            operationDesc = "发布技能服务"
    )
    public SaResult publishSkill(@RequestBody SkillPublishRequest request) {
        try {
            // 手动验证参数
            String validationError = validateSkillPublishRequest(request);
            if (validationError != null) {
                return SaResult.error(validationError);
            }

            Integer userId = StpUtil.getLoginIdAsInt();
            Long serviceId = skillServiceService.publishSkillService(userId, request);

            Map<String, Object> data = new HashMap<>();
            data.put("serviceId", serviceId);

            return SaResult.ok("发布成功").setData(data);
        } catch (IllegalArgumentException e) {
            return SaResult.error(e.getMessage());
        } catch (Exception e) {
            return SaResult.error("发布失败: " + e.getMessage());
        }
    }

    /**
     * 更新技能服务
     */
    @PutMapping("/{serviceId}")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "更新服务",
            operationDesc = "更新技能服务"
    )
    public SaResult updateSkill(@PathVariable Long serviceId,
                              @RequestBody SkillPublishRequest request) {
        try {
            // 手动验证参数
            String validationError = validateSkillPublishRequest(request);
            if (validationError != null) {
                return SaResult.error(validationError);
            }

            Integer userId = StpUtil.getLoginIdAsInt();
            boolean success = skillServiceService.updateSkillService(serviceId, userId, request);
            if (success) {
                return SaResult.ok("更新成功");
            } else {
                return SaResult.error("更新失败");
            }
        } catch (IllegalArgumentException e) {
            return SaResult.error(e.getMessage());
        } catch (Exception e) {
            return SaResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除技能服务
     */
    @DeleteMapping("/{serviceId}")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "删除服务",
            operationDesc = "删除技能服务"
    )
    public SaResult deleteSkill(@PathVariable Long serviceId) {
        try {
            Integer userId = StpUtil.getLoginIdAsInt();
            boolean success = skillServiceService.deleteSkillService(serviceId, userId);
            if (success) {
                return SaResult.ok("删除成功");
            } else {
                return SaResult.error("删除失败");
            }
        } catch (IllegalArgumentException e) {
            return SaResult.error(e.getMessage());
        } catch (Exception e) {
            return SaResult.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取我发布的服务
     */
    @GetMapping("/my")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "查询我的服务",
            operationDesc = "查询我发布的服务"
    )
    public SaResult getMySkills(@RequestParam(defaultValue = "1") Integer page,
                              @RequestParam(defaultValue = "20") Integer size,
                              @RequestParam(required = false) Integer status) {
        try {
            Integer userId = StpUtil.getLoginIdAsInt();
            IPage<SkillServiceDTO> result = skillServiceService.getUserSkillServices(userId, page, size, status);

            Map<String, Object> data = new HashMap<>();
            data.put("records", result.getRecords());
            data.put("total", result.getTotal());
            data.put("size", result.getSize());
            data.put("current", result.getCurrent());
            data.put("pages", result.getPages());

            return SaResult.ok("查询成功").setData(data);
        } catch (Exception e) {
            return SaResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取技能标签列表
     */
    @GetMapping("/tags")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "查询标签",
            operationDesc = "获取技能标签列表"
    )
    public SaResult getSkillTags(@RequestParam(required = false) Integer categoryId) {
        try {
            // 这里可以根据分类ID获取相关标签，暂时返回常用标签
            List<String> tags = List.of(
                "HTML", "CSS", "JavaScript", "Vue", "React", "Java", "Python",
                "Photoshop", "UI设计", "平面设计", "Logo设计", "海报设计",
                "英语", "日语", "口语", "雅思", "托福", "翻译",
                "钢琴", "吉他", "声乐", "绘画", "书法",
                "数学", "物理", "化学", "编程", "算法",
                "健身", "瑜伽", "篮球", "足球", "游泳",
                "摄影", "烹饪", "手工", "化妆", "舞蹈"
            );
            return SaResult.ok("获取标签成功").setData(tags);
        } catch (Exception e) {
            return SaResult.error("获取标签失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门技能服务
     */
    @GetMapping("/popular")
    @OperationLog(
            operationModule = "技能管理",
            operationType = "查询热门",
            operationDesc = "获取热门技能服务"
    )
    public SaResult getPopularSkills(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            // 这里可以实现获取热门技能服务的逻辑
            // 暂时返回空列表，可以后续完善
            return SaResult.ok("获取热门技能成功").setData(List.of());
        } catch (Exception e) {
            return SaResult.error("获取热门技能失败: " + e.getMessage());
        }
    }

    /**
     * 手动验证技能发布请求参数
     */
    private String validateSkillPublishRequest(SkillPublishRequest request) {
        if (request == null) {
            return "请求参数不能为空";
        }

        if (!StringUtils.hasText(request.getTitle())) {
            return "服务标题不能为空";
        }
        if (request.getTitle().length() > 100) {
            return "服务标题长度不能超过100个字符";
        }

        if (!StringUtils.hasText(request.getDescription())) {
            return "服务描述不能为空";
        }
        if (request.getDescription().length() > 2000) {
            return "服务描述长度不能超过2000个字符";
        }

        if (request.getPrice() == null) {
            return "服务价格不能为空";
        }
        if (request.getPrice().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            return "服务价格必须大于0";
        }
        if (request.getPrice().compareTo(BigDecimal.valueOf(9999.99)) > 0) {
            return "服务价格不能超过9999.99";
        }

        if (request.getDuration() == null) {
            return "服务时长不能为空";
        }
        if (request.getDuration() < 15) {
            return "服务时长不能少于15分钟";
        }
        if (request.getDuration() > 480) {
            return "服务时长不能超过480分钟";
        }

        if (request.getCategoryId() == null) {
            return "服务分类不能为空";
        }

        if (request.getServiceType() == null) {
            return "服务类型不能为空";
        }
        if (request.getServiceType() < 1 || request.getServiceType() > 2) {
            return "服务类型值无效";
        }

        if (request.getServiceType() == 2 && !StringUtils.hasText(request.getLocation())) {
            return "线下服务必须填写服务地点";
        }
        if (request.getLocation() != null && request.getLocation().length() > 255) {
            return "服务地点长度不能超过255个字符";
        }

        if (request.getMaxStudents() == null) {
            return "最大学员数不能为空";
        }
        if (request.getMaxStudents() < 1) {
            return "最大学员数不能少于1";
        }
        if (request.getMaxStudents() > 10) {
            return "最大学员数不能超过10";
        }

        if (request.getTags() != null && request.getTags().size() > 10) {
            return "标签数量不能超过10个";
        }

        if (request.getImages() != null && request.getImages().size() > 5) {
            return "图片数量不能超过5张";
        }

        if (request.getContent() != null && request.getContent().length() > 5000) {
            return "服务内容长度不能超过5000个字符";
        }

        return null; // 验证通过
    }
}