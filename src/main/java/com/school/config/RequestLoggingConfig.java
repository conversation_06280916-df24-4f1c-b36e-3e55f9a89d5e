package com.school.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

/**
 * 2024/10/7
 * 栋dong
 */
@Configuration
public class RequestLoggingConfig {

    @Bean
    public CommonsRequestLoggingFilter requestLoggingFilter() {
        CommonsRequestLoggingFilter filter = new CommonsRequestLoggingFilter();
        filter.setIncludeQueryString(true);  // 包括查询参数
        filter.setIncludePayload(true);      // 包括请求体
        filter.setMaxPayloadLength(10000);   // 设置最大请求体的长度
        filter.setIncludeHeaders(true);      // 包括请求头
        filter.setAfterMessagePrefix("Incoming Request: ");  // 日志前缀
        return filter;
    }
}