package com.school.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: 黄孟轩
 * @CreateTime: 2024-10-10
 * @Description: 审批实体类
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("leaves")
public class ApprovalInfo {
    /**
     * auto-id
     */
    private int id;
    /**
     * 审批时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date examineTime;
    /**
     * 审批人
     * */
    private String examinePerson;
    /**
     * 审批意见
     * */
    private String examineApproval;
    /**
     * 审核状态
     */
    private String examineStatus;
}
