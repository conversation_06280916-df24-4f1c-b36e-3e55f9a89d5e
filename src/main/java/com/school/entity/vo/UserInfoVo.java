package com.school.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 2024/9/30
 * 栋dong
 * 用户登录信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoVo {
    //用户ID
    private Integer userId;
    //用户名
    private String name;
    //学号
    private String stuId;
    //学院
    private String academy;
    //邮箱
    private String email;
    private String avatar;
    private String cardPhoto;
    //一卡通号
    private String cardId;
    private List<String> roles;
    //是否为第一次登录
    private boolean isfirstlogin;
}
