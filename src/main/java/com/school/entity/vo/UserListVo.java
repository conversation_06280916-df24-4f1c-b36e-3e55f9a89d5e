package com.school.entity.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UserListVo {
    private Integer userId;
    private String academy;
    private String name;
    private String stuId;
    private String cardId;
    private String cardPhoto;
    private String password;
    private String email;
    private Integer labStatus;
    private Date createdTime;
    private Date updatedTime;
    private List<String> roles;
    private List<String> permissions;
    private String avatar;
//    private String group;
} 