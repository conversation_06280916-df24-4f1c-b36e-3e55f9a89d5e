package com.school.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 审核记录实体
 */
@Data
@TableName("audits")
public class Audits {
    
    /**
     * 审核ID
     */
    @TableId(value = "audit_id", type = IdType.AUTO)
    private Long auditId;
    
    /**
     * 审核目标ID
     */
    private Long targetId;
    
    /**
     * 审核目标类型：user-用户，skill-技能服务
     */
    private String targetType;
    
    /**
     * 审核目标名称
     */
    private String targetName;
    
    /**
     * 审核状态：0-待审核，1-通过，2-拒绝
     */
    private Integer status;
    
    /**
     * 提交时间
     */
    private LocalDateTime submitTime;
    
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    
    /**
     * 审核员ID
     */
    private Integer auditorId;
    
    /**
     * 审核员姓名
     */
    private String auditorName;
    
    /**
     * 审核意见
     */
    private String reason;
    
    /**
     * 审核内容摘要
     */
    private String content;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
} 