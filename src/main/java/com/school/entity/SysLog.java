package com.school.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("sys_log")
public class SysLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 操作模块
     */
    private String operationModule;
    
    /**
     * 操作类型
     */
    private String operationType;
    
    /**
     * 操作描述
     */
    private String operationDesc;
    
    /**
     * 请求URL
     */
    private String requestUrl;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 请求参数
     */
    private String requestParams;
    
    /**
     * 请求头
     */
    private String requestHeaders;
    
    /**
     * 操作IP
     */
    private String operationIp;
    
    // 响应结果拆分
    private Integer responseCode;
    private String responseMsg;
    private String responseData;
    
    /**
     * 操作用户ID
     */
    private Long userId;
    
    /**
     * 操作用户名称
     */
    private String username;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 执行时长(毫秒)
     */
    private Long executionTime;
} 