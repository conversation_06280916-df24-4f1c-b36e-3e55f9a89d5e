package com.school.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 技能服务实体类
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("skill_services")
public class SkillService implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 服务ID
     */
    @TableId(value = "service_id", type = IdType.AUTO)
    private Long serviceId;
    
    /**
     * 发布者ID
     */
    private Integer userId;
    
    /**
     * 分类ID
     */
    private Integer categoryId;
    
    /**
     * 服务标题
     */
    private String title;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务价格（虚拟币）
     */
    private BigDecimal price;
    
    /**
     * 服务时长（分钟）
     */
    private Integer duration;
    
    /**
     * 服务类型：1-线上，2-线下
     */
    private Integer serviceType;
    
    /**
     * 服务地点（线下服务必填）
     */
    private String location;
    
    /**
     * 最大学员数
     */
    private Integer maxStudents;
    
    /**
     * 状态：1-待审核，2-已发布，3-已下架，4-已删除
     */
    private Integer status;
    
    /**
     * 标签（JSON格式）
     */
    private String tags;
    
    /**
     * 平均评分
     */
    private BigDecimal ratingAvg;
    
    /**
     * 评价数量
     */
    private Integer ratingCount;
    
    /**
     * 订单数量
     */
    private Integer orderCount;
    
    /**
     * 浏览次数
     */
    private Integer viewCount;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
    
    /**
     * 审核时间
     */
    private Date auditTime;
    
    /**
     * 审核意见
     */
    private String auditReason;
}
