package com.school.entity.dto;

import lombok.Data;

/**
 * 用户注册DTO
 */
@Data
public class RegisterDto {
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 确认密码
     */
    private String confirmPassword;
    
    /**
     * 学号
     */
    private String stuId;
    
    /**
     * 一卡通号
     */
    private String cardId;
    
    /**
     * 学院
     */
    private String academy;
    
    /**
     * 验证码
     */
    private String verificationCode;
} 