package com.school.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 技能服务图片实体类
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("skill_images")
public class SkillImage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 图片ID
     */
    @TableId(value = "image_id", type = IdType.AUTO)
    private Long imageId;
    
    /**
     * 服务ID
     */
    private Long serviceId;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 排序序号
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private Date createdTime;
}
