package com.school.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("users")
public class Users implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Integer userId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 姓名
     */
    private String name;

    /**
     * 学号/工号
     */
    private String stuId;

    /**
     * 学院
     */
    private String academy;

    /**
     * 一卡通号
     */
    private String cardId;

    /**
     * 一卡通照片
     */
    private String cardPhoto;

    /**
     * 用户类型：1-学生，2-教师
     */
    private Integer userType;

    /**
     * 用户状态：1-正常，2-待审核，3-已禁用，4-已注销，5-待上传证件
     */
    private Integer status;

    /**
     * 状态变更原因
     */
    private String statusReason;

    /**
     * 状态更新时间
     */
    private Date statusUpdateTime;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 信用评分
     */
    private Integer creditScore;
    
    /**
     * 信用等级
     */
    private String creditLevel;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 个人技能标签（JSON格式）
     */
    private String skills;
    
    /**
     * 粉丝数
     */
    private Integer followerCount;
    
    /**
     * 关注数
     */
    private Integer followingCount;
    
    /**
     * 是否首次登录：1-是，0-否
     */
    private Integer isFirstLogin;
    
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
}

