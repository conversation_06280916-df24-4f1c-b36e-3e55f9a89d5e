package com.school.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Sms implements Serializable {
    private static final long serialVersionUID = -98770158204496737L;
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;
    /**
     * 登录id，注册时为0
     */
    private Integer loginId;
    /**
     * 选择发送的方式，phone为电话，email为邮件
     */
    private String emailOrPhone;
    /**
     * 要发送的电话号
     */
    private String phone;
    /**
     * 要发送的邮件
     */
    private String email;
    /**
     * 用途
     */
    private String useTo;
    /**
     * 用于确认验证码的id
     */
    private String uuid;
    /**
     * 随机8位的验证码
     */
    private String sms;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 有效期，单位为秒，默认360秒
     */
    private Integer effectiveTime;
    /**
     * 使用状态，默认为-1，表示为未使用，验证成功为1，验证失败为0
     */
    private Integer status;
    /**
     * 验证时间
     */
    private Date verificationTime;
    /**
     * 验证次数
     */
    private Integer verificationNumber;
    /**
     * 删除标志（0代表未删除，1代表已删除）
     */
    private Integer defFlag;
}

