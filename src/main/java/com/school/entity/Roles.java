package com.school.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 角色表(Roles)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-14 18:12:31
 */
@Data
public class Roles extends Model<Roles> {
    //角色ID
    @TableId(type = IdType.AUTO)
    private Integer roleId;
    //角色名称
    private String roleName;
    //角色描述
    private String description;
    //创建时间
    private Date createdTime;
    //更新时间
    private Date updatedTime;

}

