package com.school.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 技能服务数据传输对象
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Data
public class SkillServiceDTO {
    
    /**
     * 服务ID
     */
    private Long serviceId;
    
    /**
     * 服务标题
     */
    private String title;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务价格
     */
    private BigDecimal price;
    
    /**
     * 服务时长（分钟）
     */
    private Integer duration;
    
    /**
     * 服务类型：1-线上，2-线下
     */
    private Integer serviceType;
    
    /**
     * 服务地点
     */
    private String location;
    
    /**
     * 最大学员数
     */
    private Integer maxStudents;
    
    /**
     * 状态：1-待审核，2-已发布，3-已下架，4-已删除
     */
    private Integer status;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 平均评分
     */
    private BigDecimal ratingAvg;
    
    /**
     * 评价数量
     */
    private Integer ratingCount;
    
    /**
     * 订单数量
     */
    private Integer orderCount;
    
    /**
     * 浏览次数
     */
    private Integer viewCount;
    
    /**
     * 分类ID
     */
    private Integer categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 发布者ID
     */
    private Integer userId;
    
    /**
     * 发布者姓名
     */
    private String userName;
    
    /**
     * 发布者头像
     */
    private String userAvatar;
    
    /**
     * 发布者学院
     */
    private String userAcademy;
    
    /**
     * 发布者信用评分
     */
    private Integer userCreditScore;
    
    /**
     * 服务图片列表
     */
    private List<String> images;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
}
