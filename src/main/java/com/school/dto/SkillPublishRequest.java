package com.school.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 技能发布请求对象
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Data
public class SkillPublishRequest {
    
    /**
     * 服务标题
     */
    private String title;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务价格
     */
    private BigDecimal price;

    /**
     * 服务时长（分钟）
     */
    private Integer duration;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 服务类型：1-线上，2-线下
     */
    private Integer serviceType;

    /**
     * 服务地点（线下服务必填）
     */
    private String location;

    /**
     * 最大学员数
     */
    private Integer maxStudents;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 服务图片列表
     */
    private List<String> images;

    /**
     * 服务内容（详细描述）
     */
    private String content;

    /**
     * 常见问题FAQ（JSON格式）
     */
    private String faqs;

    /**
     * 服务方式（JSON格式，包含线上线下等信息）
     */
    private String serviceMethod;

    /**
     * 服务时间（JSON格式）
     */
    private String serviceTime;
}
