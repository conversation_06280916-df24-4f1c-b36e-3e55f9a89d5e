package com.school.util;

import org.springframework.util.DigestUtils;
import java.lang.reflect.Field;

public class HashUtils {

    /**
     * 为对象生成哈希值并设置到指定字段
     *
     * @param <T> 对象的类型
     * @param object 要生成哈希值的对象
     * @param hashFieldName 用于存储哈希值的字段名称
     * @return 设置了哈希值的对象
     * @throws IllegalAccessException 如果反射访问失败
     * @throws NoSuchFieldException 如果指定的字段不存在
     */
    public static <T> T generateAndSetHash(T object, String hashFieldName) throws IllegalAccessException, NoSuchFieldException {
        StringBuilder stringBuilder = new StringBuilder();

        // 获取对象的所有字段，包括私有字段
        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true); // 允许访问私有字段

            // 排除哈希字段自身，避免递归哈希
            if (!field.getName().equals(hashFieldName)) {
                Object value = field.get(object);
                if (value != null) {
                    stringBuilder.append(value.toString());
                }
            }
        }

        // 生成MD5哈希值
        String hash = DigestUtils.md5DigestAsHex(stringBuilder.toString().getBytes());

        // 将哈希值设置到指定字段
        Field hashField = object.getClass().getDeclaredField(hashFieldName);
        hashField.setAccessible(true);
        hashField.set(object, hash);

        return object;
    }
}
