package com.school.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.listener.PageReadListener;
import com.school.enums.FilePurposeEnum;
import com.school.service.FileUploadService;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ExcelTool {
    public static <T> String writeExcel(List<T> list, String filename, Class<T> type, FileUploadService fileUploadService) throws Exception {
        // 先将数据写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, type).sheet(filename).doWrite(list);
        
        // 将字节数组转换为MultipartFile
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String fullFilename = filename + timeStamp + ".xlsx";
        MultipartFile file = new MockMultipartFile(
            fullFilename,
            fullFilename,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            outputStream.toByteArray()
        );
        
        // 使用FileUploadService保存文件
        return fileUploadService.saveFile(file, FilePurposeEnum.USER_LIST);
    }

    public static <T> List<T> readExcel(MultipartFile file, Class<T> type) throws Exception {
        List<T> list = new ArrayList<>();
        // 使用 EasyExcel 读取 Excel 文件
        ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(file.getInputStream(), type, new PageReadListener<T>(dataList -> {
            for (T data : dataList) {
                list.add(data);
            }
        }));
        ExcelReaderSheetBuilder sheetBuilder = excelReaderBuilder.sheet().headRowNumber(1); // 跳过第一行
        sheetBuilder.doRead();
        return list;
    }
}
