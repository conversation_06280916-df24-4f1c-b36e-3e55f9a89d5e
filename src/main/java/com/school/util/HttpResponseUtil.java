package com.school.util;

import cn.hutool.http.HttpConnection;
import cn.hutool.http.HttpResponse;

import java.lang.reflect.Field;

/**
 * 2024/9/21
 * 栋dong
 */
public class HttpResponseUtil {
    public static HttpConnection getHttpURLConnection(HttpResponse response) {
        try {
            // 使用反射获取 HttpResponse 的 HttpConnection 字段
            Field field = HttpResponse.class.getDeclaredField("httpConnection");
            field.setAccessible(true);

            // 获取 HttpConnection 对象
            HttpConnection httpConnection = (HttpConnection) field.get(response);

            // 从 HttpConnection 获取 HttpURLConnection
            return httpConnection;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }
}
