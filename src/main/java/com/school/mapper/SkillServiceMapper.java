package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.school.entity.SkillService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 技能服务Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Mapper
public interface SkillServiceMapper extends BaseMapper<SkillService> {
    
    /**
     * 分页查询技能服务列表（带用户信息和分类信息）
     */
    IPage<Map<String, Object>> selectSkillServicePage(
            Page<Map<String, Object>> page,
            @Param("keyword") String keyword,
            @Param("categoryId") Integer categoryId,
            @Param("priceMin") BigDecimal priceMin,
            @Param("priceMax") BigDecimal priceMax,
            @Param("sortBy") String sortBy
    );
    
    /**
     * 根据服务ID查询技能服务详情（带用户信息和分类信息）
     */
    Map<String, Object> selectSkillServiceDetail(@Param("serviceId") Long serviceId);
    
    /**
     * 查询用户发布的技能服务列表
     */
    IPage<Map<String, Object>> selectUserSkillServices(
            Page<Map<String, Object>> page,
            @Param("userId") Integer userId,
            @Param("status") Integer status
    );
    
    /**
     * 增加浏览次数
     */
    @Update("UPDATE skill_services SET view_count = view_count + 1 WHERE service_id = #{serviceId}")
    int incrementViewCount(@Param("serviceId") Long serviceId);
    
    /**
     * 更新评分信息
     */
    @Update("UPDATE skill_services SET rating_avg = #{ratingAvg}, rating_count = #{ratingCount} WHERE service_id = #{serviceId}")
    int updateRating(@Param("serviceId") Long serviceId, @Param("ratingAvg") BigDecimal ratingAvg, @Param("ratingCount") Integer ratingCount);
    
    /**
     * 增加订单数量
     */
    @Update("UPDATE skill_services SET order_count = order_count + 1 WHERE service_id = #{serviceId}")
    int incrementOrderCount(@Param("serviceId") Long serviceId);
    
    /**
     * 查询热门技能服务
     */
    @Select("SELECT * FROM skill_services WHERE status = 2 ORDER BY view_count DESC, order_count DESC LIMIT #{limit}")
    List<SkillService> selectPopularServices(@Param("limit") Integer limit);
}
