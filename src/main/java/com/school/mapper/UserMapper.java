package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.school.entity.UserInfo;
import com.school.entity.Users;
import com.school.entity.vo.UserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserMapper extends BaseMapper<Users> {
    List<UserInfo> fuzzyQuery(String fuzzy);
    // 继承自 BaseMapper，不需要其他操作

    List<UserVo> searchUsers(String query);

    /**
     * 批量查询用户基本信息
     * @param userIds 用户ID列表
     * @return 用户基本信息列表
     */
    List<Map<String, Object>> selectBasicInfoByIds(@Param("userIds") List<String> userIds);

    /**
     * 分页查询用户列表
     */
    List<Users> selectUserPage(@Param("offset") Integer offset, 
                              @Param("pageSize") Integer pageSize, 
                              @Param("query") String query,
                              @Param("status") Integer status);
    
    /**
     * 查询符合条件的总记录数
     */
    Integer selectUserTotal(@Param("query") String query,
                           @Param("status") Integer status);

//    Integer updateFirstLogin(Integer userId);

    /**
     * 根据角色ID获取用户ID列表
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<String> getUserIdsByRole(Integer roleId);
}
