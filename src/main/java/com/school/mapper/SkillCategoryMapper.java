package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.school.entity.SkillCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 技能分类Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Mapper
public interface SkillCategoryMapper extends BaseMapper<SkillCategory> {
    
    /**
     * 查询启用状态的分类列表，按排序序号排序
     */
    @Select("SELECT * FROM skill_categories WHERE status = 1 ORDER BY sort_order ASC, category_id ASC")
    List<SkillCategory> selectEnabledCategories();
}
