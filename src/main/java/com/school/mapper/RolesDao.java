package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.school.entity.Roles;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 角色表(Roles)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-14 18:13:31
 */
public interface RolesDao extends BaseMapper<Roles> {

    @Select("SELECT r.role_name FROM roles r " +
            "INNER JOIN user_roles ur ON r.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId}")
    List<String> getall(String userId);

    /**
     * 删除用户的所有角色
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteUserRoles(String userId);

    /**
     * 添加用户角色
     * @param userId 用户ID
     * @param role 角色名称
     * @return 影响的行数
     */
    int addUserRole(String userId, String role);

    /**
     * 获取角色下的用户列表
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<Map<String, Object>> getRoleUsers(Integer roleId);

    /**
     * 根据角色名获取角色ID
     * @param roleName 角色名称
     * @return 角色ID
     */
    @Select("SELECT role_id FROM roles WHERE role_name = #{roleName}")
    Integer getRoleIdByName(@Param("roleName") String roleName);

    /**
     * 插入用户角色关联
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 影响行数
     */
    @Insert("INSERT INTO user_roles (user_id, role_id) VALUES (#{userId}, #{roleId})")
    int insertUserRole(@Param("userId") Integer userId, @Param("roleId") Integer roleId);
}

