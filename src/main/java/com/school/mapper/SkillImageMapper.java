package com.school.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.school.entity.SkillImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 技能服务图片Mapper接口
 *
 * <AUTHOR>
 * @since 2025-03-02
 */
@Mapper
public interface SkillImageMapper extends BaseMapper<SkillImage> {
    
    /**
     * 根据服务ID查询图片列表
     */
    @Select("SELECT * FROM skill_images WHERE service_id = #{serviceId} ORDER BY sort_order ASC, image_id ASC")
    List<SkillImage> selectByServiceId(@Param("serviceId") Long serviceId);
}
