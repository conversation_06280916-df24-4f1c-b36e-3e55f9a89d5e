package com.school.enums;

/**
 * 用户实验室状态枚举
 * 2025/2/18
 *
 * <AUTHOR>
 */
public enum UserLabStatus {
    /**
     * 正常状态
     */
    NORMAL(1, "正常"),
    
    /**
     * 待认证状态
     */
    PENDING(2, "待认证"),
    
    /**
     * 已开除状态
     */
    EXPELLED(3, "已开除"),
    
    /**
     * 已退出状态
     */
    QUIT(4, "已退出"),

    /**
     * 已毕业状态
     */
    GRADUATED(5, "已毕业");

    private final Integer code;
    private final String description;

    UserLabStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static UserLabStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserLabStatus status : UserLabStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
