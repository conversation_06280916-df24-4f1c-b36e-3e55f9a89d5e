package com.school.enums;

import java.util.Arrays;
import java.util.List;

/**
 * 文件用途枚举
 */
public enum FilePurposeEnum {
    /** 头像 */
    AVATAR("avatar", "头像", Arrays.asList("jpg", "jpeg", "png")),
    /** 一卡通照片 */
    CARD("card" ,"一卡通", Arrays.asList("jpg", "jpeg", "png")),
    /** 用户导入 */
    USER_LIST("userList", "用户列表", Arrays.asList("xlsx", "xls")),
    /** 技能封面图 */
    SKILL_COVER("skillcover", "技能封面图", Arrays.asList("jpg", "jpeg", "png", "webp")),
    /** 技能服务图片 */
    SKILL_IMAGE("skillimage", "技能服务图片", Arrays.asList("jpg", "jpeg", "png", "webp")),
    /** 其他 */
    OTHER("other", "其他", Arrays.asList("jpg", "jpeg", "png", "pdf", "doc", "docx","xlsx","xls"));

    private final String code;
    private final String description;
    private final List<String> allowedExtensions;

    FilePurposeEnum(String code, String description, List<String> allowedExtensions) {
        this.code = code;
        this.description = description;
        this.allowedExtensions = allowedExtensions;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public List<String> getAllowedExtensions() {
        return allowedExtensions;
    }

    /**
     * 根据code获取枚举值
     */
    public static FilePurposeEnum getByCode(String code) {
        if (code == null) {
            return OTHER;
        }
        for (FilePurposeEnum purpose : values()) {
            if (purpose.getCode().equals(code.toLowerCase())) {
                return purpose;
            }
        }
        return OTHER;
    }

    /**
     * 判断code是否有效
     */
    public static boolean isValid(String code) {
        if (code == null) {
            return false;
        }
        for (FilePurposeEnum purpose : values()) {
            if (purpose.getCode().equals(code.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文件扩展名是否允许
     */
    public boolean isExtensionAllowed(String extension) {
        if (extension == null) {
            return false;
        }
        return allowedExtensions.contains(extension.toLowerCase());
    }
}
 