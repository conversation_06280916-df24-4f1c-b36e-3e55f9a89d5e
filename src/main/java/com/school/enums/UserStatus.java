package com.school.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户状态枚举
 */
@Getter
@AllArgsConstructor
public enum UserStatus {
    /**
     * 正常状态
     */
    NORMAL(1, "正常"),
    /**
     * 待审核状态
     */
    PENDING_REVIEW(2, "待审核"),
    /**
     * 已禁用状态
     */
    BANNED(3, "已禁用"),
    /**
     * 已注销状态
     */
    DELETED(4, "已注销"),
    /**
     * 待上传证件状态
     */
    PENDING_UPLOAD(5, "待上传证件");

    /**
     * 状态码
     */
    private final Integer code;
    /**
     * 状态描述
     */
    private final String description;

    /**
     * 获取状态码
     *
     * @return 状态码
     */
    public Integer getCode() {
        return this.code;
    }

    /**
     * 获取状态描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * 根据状态码获取对应的枚举值
     * @param code 状态码
     * @return 状态枚举
     */
    public static UserStatus getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserStatus status : UserStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 