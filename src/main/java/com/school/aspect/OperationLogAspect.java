package com.school.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import com.school.annotation.OperationLog;
import com.school.entity.SysLog;
import com.school.mapper.SysLogMapper;
import com.school.mapper.UserMapper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Aspect
@Component
public class OperationLogAspect {


    @Autowired
    private SysLogMapper sysLogMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Around("@annotation(com.school.annotation.OperationLog)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        Object result = null;
        long loginId = -1L;
        String name = "";
        
        // 获取方法信息
        MethodSignature signature = (MethodSignature) point.getSignature();
        String methodName = signature.getMethod().getName();
        String className = point.getTarget().getClass().getName();
        
        log.debug("开始执行方法: {}.{}", className, methodName);
        
        // 记录请求参数
        Object[] args = point.getArgs();
        log.debug("方法参数: {}", objectMapper.writeValueAsString(args));
        
        if (StpUtil.isLogin()) {
            loginId = StpUtil.getLoginIdAsLong();
            name = userMapper.selectById(StpUtil.getLoginIdAsInt()).getName();
            log.debug("当前登录用户: {}, ID: {}", name, loginId);
        }
        
        try {
            // 执行方法
            result = point.proceed();
            log.debug("方法执行完成: {}.{}, 返回结果: {}", className, methodName, objectMapper.writeValueAsString(result));
            return result;
        } catch (Exception e) {
            log.error("方法执行异常: {}.{}", className, methodName);
            log.error("异常类型: {}", e.getClass().getName());
            log.error("异常信息: {}", e.getMessage());
            log.error("异常堆栈: ", e);  // 输出完整堆栈
            throw e;
        } finally {
            try {
                saveLog(point, result, loginId, name, System.currentTimeMillis() - beginTime);
                log.debug("操作日志保存成功");
            } catch (Exception e) {
                log.error("保存操作日志失败: {}", e.getMessage(), e);
            }
        }
    }

    private void saveLog(ProceedingJoinPoint joinPoint, Object result, long userId, String name, long executionTime) throws Exception {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLog operationLog = method.getAnnotation(OperationLog.class);

        SysLog sysLog = new SysLog();

        // 设置注解相关属性
        sysLog.setOperationModule(operationLog.operationModule());
        sysLog.setOperationType(operationLog.operationType());
        sysLog.setOperationDesc(operationLog.operationDesc());

        // 获取请求相关信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            sysLog.setRequestUrl(request.getRequestURI());
            sysLog.setRequestMethod(request.getMethod());

            // 请求参数处理
            Object[] args = joinPoint.getArgs();
            Object[] processedArgs = new Object[args.length];
            for (int i = 0; i < args.length; i++) {
                if (args[i] instanceof MultipartFile) {
                    MultipartFile file = (MultipartFile) args[i];
                    processedArgs[i] = Map.of(
                        "fileName", file.getOriginalFilename(),
                        "fileSize", file.getSize(),
                        "contentType", file.getContentType()
                    );
                } else {
                    processedArgs[i] = args[i];
                }
            }
            sysLog.setRequestParams(objectMapper.writeValueAsString(processedArgs));

            // 请求头处理
            Map<String, String> headerMap = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headerMap.put(headerName, request.getHeader(headerName));
            }
            sysLog.setRequestHeaders(objectMapper.writeValueAsString(headerMap));
            sysLog.setOperationIp(getIpAddress(request));
        }

        // 响应结果处理
        if (result != null) {
            try {
                // 尝试将结果转换为通用响应格式
                if (result instanceof Map) {
                    Map<String, Object> responseMap = (Map<String, Object>) result;
                    sysLog.setResponseCode(responseMap.containsKey("code") ? ((Number) responseMap.get("code")).intValue() : 0);
                    sysLog.setResponseMsg((String) responseMap.get("msg"));
                    sysLog.setResponseData(objectMapper.writeValueAsString(responseMap.get("data")));
                } else {
                    // 如果不是标准响应格式，则将整个结果放在data中
                    sysLog.setResponseCode(200);
                    sysLog.setResponseMsg("success");
                    sysLog.setResponseData(objectMapper.writeValueAsString(result));
                }
            } catch (Exception e) {
                log.warn("响应结果处理异常", e);
                sysLog.setResponseCode(500);
                sysLog.setResponseMsg(e.getMessage());
                sysLog.setResponseData("");
            }
        }

        // 设置用户信息
        if (userId == -1L) {
            if (StpUtil.isLogin()) {
                sysLog.setUserId(StpUtil.getLoginIdAsLong());
                sysLog.setUsername(userMapper.selectById(StpUtil.getLoginIdAsInt()).getName());
            }
        } else {
            sysLog.setUserId(userId);
            sysLog.setUsername(name);
        }

        // 设置操作时间，直接使用当前时间，依赖数据库时区设置
        sysLog.setOperationTime(LocalDateTime.now());
        sysLog.setExecutionTime(executionTime);

        // 保存日志
        sysLogMapper.insert(sysLog);
    }

    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 如果有多个 IP，通过逗号分割取第一个非unknown 的 IP
            String[] ips = ip.split(",");
            for (String candidate : ips) {
                candidate = candidate.trim();
                if (candidate.length() != 0 && !"unknown".equalsIgnoreCase(candidate)) {
                    ip = candidate;
                    break;
                }
            }
            return ip;
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        ip = request.getHeader("X-Real-IP");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }

        // 如果以上头信息都无效，则使用 request.getRemoteAddr() 获取IP
        return request.getRemoteAddr();
    }
} 