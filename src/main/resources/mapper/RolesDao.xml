<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.school.mapper.RolesDao">

    <resultMap type="com.school.entity.Roles" id="RolesMap">
        <result property="roleId" column="role_id" jdbcType="INTEGER"/>
        <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="roleId" useGeneratedKeys="true">
        insert into roles(role_name, description, created_time, updated_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.roleName}, #{entity.description}, #{entity.createdTime}, #{entity.updatedTime})
        </foreach>
    </insert>

    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="roleId" useGeneratedKeys="true">
        insert into roles(role_name, description, created_time, updated_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.roleName}, #{entity.description}, #{entity.createdTime}, #{entity.updatedTime})
        </foreach>
        on duplicate key update
        role_name = values(role_name),
        description = values(description),
        created_time = values(created_time),
        updated_time = values(updated_time)
    </insert>

    <!-- 获取用户的所有角色 -->
    <select id="getall" resultType="java.lang.String">
        SELECT r.role_name
        FROM roles r
        INNER JOIN user_roles ur ON r.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
    </select>

    <!-- 删除用户的所有角色 -->
    <delete id="deleteUserRoles">
        DELETE FROM user_roles
        WHERE user_id = #{userId}
    </delete>

    <!-- 添加用户角色 -->
    <insert id="addUserRole">
        INSERT INTO user_roles (user_id, role_id)
        SELECT #{userId}, role_id
        FROM roles
        WHERE role_name = #{role}
    </insert>

    <!-- 获取角色下的用户列表 -->
    <select id="getRoleUsers" resultType="java.util.Map">
        SELECT 
            u.user_id as userId, 
            u.name, 
            u.stu_id as stuId
        FROM users u
        JOIN user_roles ur ON u.user_id = ur.user_id
        JOIN roles r ON ur.role_id = r.role_id
        WHERE r.role_id = #{roleId}
    </select>

</mapper>

