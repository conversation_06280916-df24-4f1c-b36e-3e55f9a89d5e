<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.school.mapper.PermissionsMapper">
    <select id="getall" resultType="java.lang.String">
        SELECT
            p.permission_name
        FROM
            permissions p
                JOIN role_permissions rp ON p.permission_id = rp.permission_id
                JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE
            ur.user_id = #{userId}
    </select>
</mapper>