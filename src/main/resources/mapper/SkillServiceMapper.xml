<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.school.mapper.SkillServiceMapper">

    <!-- 分页查询技能服务列表（带用户信息和分类信息） -->
    <select id="selectSkillServicePage" resultType="java.util.Map">
        SELECT 
            ss.service_id,
            ss.title,
            ss.description,
            ss.price,
            ss.duration,
            ss.service_type,
            ss.location,
            ss.max_students,
            ss.status,
            ss.tags,
            ss.rating_avg,
            ss.rating_count,
            ss.order_count,
            ss.view_count,
            ss.created_time,
            ss.updated_time,
            ss.category_id,
            sc.name as category_name,
            ss.user_id,
            u.name as user_name,
            u.avatar as user_avatar,
            u.academy as user_academy,
            u.credit_score as user_credit_score
        FROM skill_services ss
        LEFT JOIN skill_categories sc ON ss.category_id = sc.category_id
        LEFT JOIN users u ON ss.user_id = u.user_id
        WHERE ss.status = 2
        <if test="keyword != null and keyword != ''">
            AND (ss.title LIKE CONCAT('%', #{keyword}, '%') 
                 OR ss.description LIKE CONCAT('%', #{keyword}, '%')
                 OR ss.tags LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="categoryId != null">
            AND ss.category_id = #{categoryId}
        </if>
        <if test="priceMin != null">
            AND ss.price &gt;= #{priceMin}
        </if>
        <if test="priceMax != null">
            AND ss.price &lt;= #{priceMax}
        </if>
        <choose>
            <when test="sortBy == 'price_asc'">
                ORDER BY ss.price ASC
            </when>
            <when test="sortBy == 'price_desc'">
                ORDER BY ss.price DESC
            </when>
            <when test="sortBy == 'rating'">
                ORDER BY ss.rating_avg DESC, ss.rating_count DESC
            </when>
            <when test="sortBy == 'popular'">
                ORDER BY ss.view_count DESC, ss.order_count DESC
            </when>
            <when test="sortBy == 'newest'">
                ORDER BY ss.created_time DESC
            </when>
            <otherwise>
                ORDER BY ss.created_time DESC
            </otherwise>
        </choose>
    </select>

    <!-- 根据服务ID查询技能服务详情（带用户信息和分类信息） -->
    <select id="selectSkillServiceDetail" resultType="java.util.Map">
        SELECT 
            ss.service_id,
            ss.title,
            ss.description,
            ss.price,
            ss.duration,
            ss.service_type,
            ss.location,
            ss.max_students,
            ss.status,
            ss.tags,
            ss.rating_avg,
            ss.rating_count,
            ss.order_count,
            ss.view_count,
            ss.created_time,
            ss.updated_time,
            ss.category_id,
            sc.name as category_name,
            sc.description as category_description,
            ss.user_id,
            u.name as user_name,
            u.avatar as user_avatar,
            u.academy as user_academy,
            u.credit_score as user_credit_score,
            u.credit_level as user_credit_level,
            u.bio as user_bio,
            u.skills as user_skills
        FROM skill_services ss
        LEFT JOIN skill_categories sc ON ss.category_id = sc.category_id
        LEFT JOIN users u ON ss.user_id = u.user_id
        WHERE ss.service_id = #{serviceId}
    </select>

    <!-- 查询用户发布的技能服务列表 -->
    <select id="selectUserSkillServices" resultType="java.util.Map">
        SELECT 
            ss.service_id,
            ss.title,
            ss.description,
            ss.price,
            ss.duration,
            ss.service_type,
            ss.location,
            ss.max_students,
            ss.status,
            ss.tags,
            ss.rating_avg,
            ss.rating_count,
            ss.order_count,
            ss.view_count,
            ss.created_time,
            ss.updated_time,
            ss.category_id,
            sc.name as category_name,
            ss.user_id,
            u.name as user_name,
            u.avatar as user_avatar,
            u.academy as user_academy,
            u.credit_score as user_credit_score
        FROM skill_services ss
        LEFT JOIN skill_categories sc ON ss.category_id = sc.category_id
        LEFT JOIN users u ON ss.user_id = u.user_id
        WHERE ss.user_id = #{userId}
        AND ss.status &lt;&gt; 4
        <if test="status != null">
            AND ss.status = #{status}
        </if>
        ORDER BY ss.created_time DESC
    </select>

</mapper>
