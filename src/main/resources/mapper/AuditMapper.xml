<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.school.mapper.AuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.school.entity.Audits">
        <id column="audit_id" property="auditId" />
        <result column="target_id" property="targetId" />
        <result column="target_type" property="targetType" />
        <result column="target_name" property="targetName" />
        <result column="status" property="status" />
        <result column="submit_time" property="submitTime" />
        <result column="audit_time" property="auditTime" />
        <result column="auditor_id" property="auditorId" />
        <result column="auditor_name" property="auditorName" />
        <result column="reason" property="reason" />
        <result column="content" property="content" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        audit_id, target_id, target_type, target_name, status, submit_time, audit_time, 
        auditor_id, auditor_name, reason, content, created_time, updated_time
    </sql>

</mapper> 