# application-dev.yml
spring:
  datasource:
  #    url: *********************************************************************************************************************************
  #    username: root
  #    password: cust3697927dmsy
    url: **************************************************************************************************************************************
    username: root
    password: mysql8dong
#    url: jdbc:mysql://***************:22076/CodeAcademyManageSystem?serverTimezone=Asia/Shanghai&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull
#    username: root
#    password: cust3697927!@#
  data:
    redis:
      database: 1
      host: localhost # Redis 主机地址
      port: 6379      # Redis 端口号
  mail:
      host: smtp.qq.com
      port: 587
      username: <EMAIL>
      password: vqyqvqqpnknjchdi
#    host: mail6.serv00.com
#    port: 465
#    username: <EMAIL>
#    password: awLr*{k814DXpg9qS4Pn<6piugE8B4
      properties.mail.smtp.auth: true
      properties.mail.smtp.starttls.enable: true

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0
      #configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #mybatisPlus的日志