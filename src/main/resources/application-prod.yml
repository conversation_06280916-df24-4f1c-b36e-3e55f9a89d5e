# application-prod.yml
spring:
  datasource:
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
  data:
    redis:
      database: ${SPRING_REDIS_DATABASE}
      host: ${SPRING_REDIS_HOST} # Redis 主机地址
      port: ${SPRING_REDIS_PORT} # Redis 端口号
  mail:
    host: mail6.serv00.com
    port: 465
    username: <EMAIL>
    password: awLr*{k814DXpg9qS4Pn<6piugE8B4

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: delFlag  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0
      #configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #mybatisPlus的日志