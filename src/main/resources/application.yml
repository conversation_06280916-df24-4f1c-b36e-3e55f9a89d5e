# application.yml
spring:
  web:
    resources:
      static-locations: file:${user.dir}/upload/,classpath:/static/
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev} # 默认使用开发环境配置
  jackson:
    time-zone: Asia/Shanghai
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
  data:
    redis:
      timeout: 10s    # Redis 连接超时时间
      lettuce:
        pool:
          enabled: true        # 启用 Lettuce 连接池
          max-active: 200       # 最大活跃连接数（并发连接数量）
          max-wait: 1000ms      # 连接池中无连接时最大等待时间
          max-idle: 10          # 连接池中最大空闲连接数
          min-idle: 0           # 连接池中最小空闲连接数
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# 应用配置
app:
  name: 校园技能共享平台
  email:
    templates:
      register-subject: "${app.name} - 注册验证码"
      login-subject: "${app.name} - 登录验证码"

server:
  port: 8081
############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: satoken
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    root: INFO
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: DEBUG
    com.school: debug  # 修改为整个项目包的日志级别
    com.school.mapper.*: debug
    com.school.aspect: debug
    com.school.controller: debug  # 添加控制器的日志级别
    org.springframework.web: debug  # 添加 Spring Web 的日志级别
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log  # 添加文件日志
file:
  upload:
    path: ${user.dir}/upload  # 文件上传根路径，使用项目根目录

