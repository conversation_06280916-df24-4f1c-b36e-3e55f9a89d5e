# 技能列表页面后端接口集成说明

## 修改概述

将技能列表页面从使用模拟数据改为调用真实的后端接口，实现完整的数据交互功能。

## 主要修改内容

### 1. 前端页面重构 (`schoolweb/src/views/skill/index.vue`)

#### 1.1 导入依赖
```typescript
import { fetchSkillList, fetchSkillCategories, type SkillService, type SkillCategory } from '@/service/api/skill';
import { getFileUrl } from '@/service/api/file';
```

#### 1.2 数据结构调整
```typescript
// 原来的模拟数据
const skillList = reactive([...]) // 删除

// 新的响应式数据
const skillList = ref<SkillService[]>([]);
const categoryOptions = ref<{ label: string; value: number }[]>([]);
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
  pages: 0
});
```

#### 1.3 API调用函数

**加载分类数据**：
```typescript
async function loadCategories() {
  try {
    const response = await fetchSkillCategories();
    // 处理多种响应格式
    if (Array.isArray(response)) {
      categoryOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.categoryId
      }));
    } else if (response && response.data && Array.isArray(response.data)) {
      // 处理包含data字段的响应
    }
  } catch (error) {
    console.error('加载分类失败:', error);
  }
}
```

**加载技能列表**：
```typescript
async function loadSkillList(isRefresh = false) {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchValue.value || undefined,
      categoryId: selectedCategories.value.length > 0 ? selectedCategories.value[0] : undefined,
      priceMin: priceRange.value[0],
      priceMax: priceRange.value[1],
      sortBy: 'created_time'
    };

    const response = await fetchSkillList(params);
    
    if (response) {
      skillList.value = response.records || [];
      pagination.total = response.total || 0;
      pagination.pages = response.pages || 0;
    }
  } catch (error) {
    console.error('加载技能列表失败:', error);
  } finally {
    loading.value = false;
  }
}
```

#### 1.4 生命周期和监听器
```typescript
// 初始化数据
onMounted(async () => {
  await loadCategories();
  await loadSkillList();
});

// 监听搜索条件变化
watch([searchValue, selectedCategories, priceRange], () => {
  loadSkillList(true);
}, { deep: true });
```

#### 1.5 模板更新

**加载状态**：
```vue
<NSpin :show="loading">
  <!-- 技能列表内容 -->
</NSpin>
```

**数据绑定**：
```vue
<NGi v-for="skill in skillList" :key="skill.id">
  <NCard @click="goToSkillDetail(skill.id)">
    <!-- 使用真实数据字段 -->
    <div>{{ skill.title }}</div>
    <div>{{ skill.description }}</div>
    <div>{{ skill.price }} 学币</div>
  </NCard>
</NGi>
```

**分页组件**：
```vue
<NPagination
  v-model:page="pagination.page"
  :page-count="pagination.pages"
  :page-size="pagination.size"
  :item-count="pagination.total"
  @update:page="loadSkillList"
/>
```

### 2. API接口优化 (`schoolweb/src/service/api/skill.ts`)

#### 2.1 修复返回类型
```typescript
// 原来
export function fetchSkillList(params: SkillSearchParams) {
  return request<ApiResponse<SkillListResult>>({
    url: '/skill/list',
    method: 'get',
    params
  });
}

// 修改后
export function fetchSkillList(params: SkillSearchParams) {
  return request<SkillListResult>({
    url: '/skill/list',
    method: 'get',
    params
  }).then(res => res.data);
}
```

### 3. 后端接口确认

后端控制器 (`src/main/java/com/school/controller/skill/SkillController.java`) 已经实现了真实的数据查询：

```java
@GetMapping("/list")
public SaResult getSkillList(@RequestParam(defaultValue = "1") Integer page,
                           @RequestParam(defaultValue = "20") Integer size,
                           @RequestParam(required = false) Integer categoryId,
                           @RequestParam(required = false) String keyword,
                           @RequestParam(required = false) String sortBy,
                           @RequestParam(required = false) BigDecimal priceMin,
                           @RequestParam(required = false) BigDecimal priceMax) {
    try {
        IPage<SkillServiceDTO> result = skillServiceService.getSkillServicePage(
                page, size, keyword, categoryId, priceMin, priceMax, sortBy);

        Map<String, Object> data = new HashMap<>();
        data.put("records", result.getRecords());
        data.put("total", result.getTotal());
        data.put("size", result.getSize());
        data.put("current", result.getCurrent());
        data.put("pages", result.getPages());

        return SaResult.ok("查询成功").setData(data);
    } catch (Exception e) {
        return SaResult.error("查询失败: " + e.getMessage());
    }
}
```

## 新增功能

### 1. 实时搜索
- 搜索关键词变化时自动刷新列表
- 分类筛选支持多选
- 价格区间筛选

### 2. 分页功能
- 支持页码切换
- 支持每页数量调整
- 显示总数和页数信息

### 3. 加载状态
- 加载时显示 Spin 组件
- 错误处理和用户提示
- 空状态展示

### 4. 图片处理
- 支持真实的封面图片
- 使用 `getFileUrl` 处理文件路径
- 默认图片兜底

## 数据字段映射

| 前端显示 | 后端字段 | 说明 |
|---------|---------|------|
| 技能标题 | title | 服务标题 |
| 技能描述 | description | 服务描述 |
| 价格 | price | 服务价格（学币） |
| 分类 | categoryId | 分类ID |
| 评分 | ratingAvg | 平均评分 |
| 评价数 | ratingCount | 评价数量 |
| 提供者 | providerName | 发布者姓名 |
| 封面图 | coverImage | 封面图片路径 |

## 测试要点

### 1. 数据加载
- [ ] 页面初始化时正确加载技能列表
- [ ] 分类数据正确加载和显示
- [ ] 分页功能正常工作

### 2. 搜索功能
- [ ] 关键词搜索生效
- [ ] 分类筛选生效
- [ ] 价格区间筛选生效
- [ ] 清空筛选条件功能正常

### 3. 交互功能
- [ ] 点击技能卡片跳转到详情页
- [ ] 分页切换正常
- [ ] 加载状态显示正确

### 4. 错误处理
- [ ] 网络错误时显示错误提示
- [ ] 空数据时显示空状态
- [ ] 加载失败时可以重试

## 后续优化建议

1. **性能优化**：
   - 实现虚拟滚动（大量数据时）
   - 添加数据缓存机制
   - 优化图片加载

2. **用户体验**：
   - 添加骨架屏
   - 实现无限滚动
   - 优化搜索防抖

3. **功能扩展**：
   - 添加排序选项
   - 支持收藏功能
   - 添加筛选历史

通过这次修改，技能列表页面已经完全集成了后端接口，实现了真实的数据交互功能。
