// vite.config.ts
import process3 from "node:process";
import { URL, fileURLToPath } from "node:url";
import { defineConfig, loadEnv } from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/vite@5.4.1_@types+node@22.4.1_sass@1.77.8/node_modules/vite/dist/node/index.js";
import AutoImport from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-auto-import@0.18.2_@vueuse+core@11.0.1_vue@3.4.38_typescript@5.5.4___rollup@4.21.0/node_modules/unplugin-auto-import/dist/vite.js";
import Components2 from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-vue-components@0.27.4_@babel+parser@7.25.3_rollup@4.21.0_vue@3.4.38_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-vue-components@0.27.4_@babel+parser@7.25.3_rollup@4.21.0_vue@3.4.38_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/resolvers.js";

// src/utils/service.ts
import json5 from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/json5@2.2.3/node_modules/json5/lib/index.js";
function createServiceConfig(env) {
  const { VITE_SERVICE_BASE_URL, VITE_OTHER_SERVICE_BASE_URL } = env;
  let other = {};
  try {
    other = json5.parse(VITE_OTHER_SERVICE_BASE_URL);
  } catch (error) {
    console.error("VITE_OTHER_SERVICE_BASE_URL is not a valid json5 string");
  }
  const httpConfig = {
    baseURL: VITE_SERVICE_BASE_URL,
    other
  };
  const otherHttpKeys = Object.keys(httpConfig.other);
  const otherConfig = otherHttpKeys.map((key) => {
    return {
      key,
      baseURL: httpConfig.other[key],
      proxyPattern: createProxyPattern(key)
    };
  });
  const config = {
    baseURL: httpConfig.baseURL,
    proxyPattern: createProxyPattern(),
    other: otherConfig
  };
  return config;
}
function createProxyPattern(key) {
  if (!key) {
    return "/proxy-default";
  }
  return `/proxy-${key}`;
}

// build/config/proxy.ts
function createViteProxy(env, enable) {
  const isEnableHttpProxy = enable && env.VITE_HTTP_PROXY === "Y";
  if (!isEnableHttpProxy) return void 0;
  const { baseURL, proxyPattern, other } = createServiceConfig(env);
  const proxy = createProxyItem({ baseURL, proxyPattern });
  other.forEach((item) => {
    Object.assign(proxy, createProxyItem(item));
  });
  return proxy;
}
function createProxyItem(item) {
  const proxy = {};
  proxy[item.proxyPattern] = {
    target: item.baseURL,
    changeOrigin: true,
    rewrite: (path3) => path3.replace(new RegExp(`^${item.proxyPattern}`), "")
  };
  return proxy;
}

// build/config/time.ts
import dayjs from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/dayjs@1.11.12/node_modules/dayjs/dayjs.min.js";
import utc from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/dayjs@1.11.12/node_modules/dayjs/plugin/utc.js";
import timezone from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/dayjs@1.11.12/node_modules/dayjs/plugin/timezone.js";
function getBuildTime() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const buildTime = dayjs.tz(Date.now(), "Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");
  return buildTime;
}

// build/plugins/index.ts
import vue from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/@vitejs+plugin-vue@5.1.2_vite@5.4.1_@types+node@22.4.1_sass@1.77.8__vue@3.4.38_typescript@5.5.4_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.1_vite@5.4.1_@types+node@22.4.1_sass@1.77.8__vue@3.4.38_typescript@5.5.4_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import VueDevtools from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.8_rollup@4.21.0_vite@5.4.1_@types+node@22.4.1_sass@1.77.8__vue@3.4.38_typescript@5.5.4_/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import progress from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/vite-plugin-progress@0.0.7_vite@5.4.1_@types+node@22.4.1_sass@1.77.8_/node_modules/vite-plugin-progress/dist/index.mjs";

// build/plugins/router.ts
import ElegantVueRouter from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/@elegant-router+vue@0.3.8/node_modules/@elegant-router/vue/dist/vite.mjs";
function setupElegantRouter() {
  return ElegantVueRouter({
    layouts: {
      base: "src/layouts/base-layout/index.vue",
      blank: "src/layouts/blank-layout/index.vue"
    },
    routePathTransformer(routeName, routePath) {
      const key = routeName;
      if (key === "login") {
        const modules = ["pwd-login", "code-login", "register", "reset-pwd", "bind-wechat"];
        const moduleReg = modules.join("|");
        return `/login/:module(${moduleReg})?`;
      }
      return routePath;
    },
    onRouteMetaGen(routeName) {
      const key = routeName;
      const constantRoutes = ["login", "403", "404", "500"];
      const meta = {
        title: key,
        i18nKey: `route.${key}`
      };
      if (constantRoutes.includes(key)) {
        meta.constant = true;
      }
      return meta;
    }
  });
}

// build/plugins/unocss.ts
import process from "node:process";
import path from "node:path";
import unocss from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/@unocss+vite@0.62.2_rollup@4.21.0_vite@5.4.1_@types+node@22.4.1_sass@1.77.8_/node_modules/@unocss/vite/dist/index.mjs";
import presetIcons from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/@unocss+preset-icons@0.62.2/node_modules/@unocss/preset-icons/dist/index.mjs";
import { FileSystemIconLoader } from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/@iconify+utils@2.1.30/node_modules/@iconify/utils/lib/loader/node-loaders.mjs";
function setupUnocss(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path.join(process.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  return unocss({
    presets: [
      presetIcons({
        prefix: `${VITE_ICON_PREFIX}-`,
        scale: 1,
        extraProperties: {
          display: "inline-block"
        },
        collections: {
          [collectionName]: FileSystemIconLoader(
            localIconPath,
            (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
          )
        },
        warn: true
      })
    ]
  });
}

// build/plugins/unplugin.ts
import process2 from "node:process";
import path2 from "node:path";
import Icons from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-icons@0.19.2_@vue+compiler-sfc@3.4.38/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-icons@0.19.2_@vue+compiler-sfc@3.4.38/node_modules/unplugin-icons/dist/resolver.js";
import Components from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-vue-components@0.27.4_@babel+parser@7.25.3_rollup@4.21.0_vue@3.4.38_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/vite.js";
import { AntDesignVueResolver, NaiveUiResolver } from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-vue-components@0.27.4_@babel+parser@7.25.3_rollup@4.21.0_vue@3.4.38_typescript@5.5.4_/node_modules/unplugin-vue-components/dist/resolvers.js";
import { FileSystemIconLoader as FileSystemIconLoader2 } from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/unplugin-icons@0.19.2_@vue+compiler-sfc@3.4.38/node_modules/unplugin-icons/dist/loaders.js";
import { createSvgIconsPlugin } from "file:///E:/AllCode/web/CodeAcademyManageSystem-web/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.4.1_@types+node@22.4.1_sass@1.77.8_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function setupUnplugin(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path2.join(process2.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  const plugins = [
    Icons({
      compiler: "vue3",
      customCollections: {
        [collectionName]: FileSystemIconLoader2(
          localIconPath,
          (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        )
      },
      scale: 1,
      defaultClass: "inline-block"
    }),
    Components({
      dts: "src/typings/components.d.ts",
      types: [{ from: "vue-router", names: ["RouterLink", "RouterView"] }],
      resolvers: [
        AntDesignVueResolver({
          importStyle: false
        }),
        NaiveUiResolver(),
        IconsResolver({ customCollections: [collectionName], componentPrefix: VITE_ICON_PREFIX })
      ]
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: "body-last",
      customDomId: "__SVG_ICON_LOCAL__"
    })
  ];
  return plugins;
}

// build/plugins/html.ts
function setupHtmlPlugin(buildTime) {
  const plugin = {
    name: "html-plugin",
    apply: "build",
    transformIndexHtml(html) {
      return html.replace("<head>", `<head>
    <meta name="buildTime" content="${buildTime}">`);
    }
  };
  return plugin;
}

// build/plugins/index.ts
function setupVitePlugins(viteEnv, buildTime) {
  const plugins = [
    vue({
      script: {
        defineModel: true
      }
    }),
    vueJsx(),
    VueDevtools(),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime)
  ];
  return plugins;
}

// vite.config.ts
var __vite_injected_original_import_meta_url = "file:///E:/AllCode/web/CodeAcademyManageSystem-web/vite.config.ts";
var vite_config_default = defineConfig((configEnv) => {
  const viteEnv = loadEnv(configEnv.mode, process3.cwd());
  const buildTime = getBuildTime();
  const enableProxy = configEnv.command === "serve" && !configEnv.isPreview;
  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        "~": fileURLToPath(new URL("./", __vite_injected_original_import_meta_url)),
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    plugins: [
      setupVitePlugins(viteEnv, buildTime),
      AutoImport({
        resolvers: [ElementPlusResolver()]
      }),
      Components2({
        resolvers: [ElementPlusResolver()]
      })
    ],
    define: {
      BUILD_TIME: JSON.stringify(buildTime)
    },
    server: {
      host: "0.0.0.0",
      port: 9527,
      open: true,
      proxy: createViteProxy(viteEnv, enableProxy),
      fs: {
        cachedChecks: false
      }
    },
    preview: {
      port: 9725
    },
    build: {
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP === "Y",
      commonjsOptions: {
        ignoreTryCatch: false
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
