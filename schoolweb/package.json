{"name": "soybean-admin", "type": "module", "version": "1.3.4", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build  --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.6.1", "@iconify/vue": "4.1.2", "@vueuse/core": "11.0.1", "axios": "^1.6.8", "clipboard": "2.0.11", "dayjs": "1.11.12", "echarts": "5.5.1", "element-plus": "^2.8.1", "marked": "^15.0.7", "naive-ui": "2.39.0", "nprogress": "0.2.0", "pinia": "2.2.2", "tailwind-merge": "2.5.2", "timetables": "^1.1.0", "typecheck": "^0.1.2", "vue": "3.4.38", "vue-draggable-plus": "0.5.3", "vue-i18n": "9.14.0", "vue-router": "4.4.3"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.238", "@soybeanjs/eslint-config": "1.4.0", "@types/node": "22.4.1", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.62.2", "@unocss/preset-icons": "0.62.2", "@unocss/preset-uno": "0.62.2", "@unocss/transformer-directives": "0.62.2", "@unocss/transformer-variant-group": "0.62.2", "@unocss/vite": "0.62.2", "@vitejs/plugin-vue": "5.1.2", "@vitejs/plugin-vue-jsx": "4.0.1", "eslint": "9.9.0", "eslint-plugin-vue": "9.27.0", "install": "^0.13.0", "json5": "2.2.3", "lint-staged": "15.2.9", "sass": "1.77.8", "simple-git-hooks": "2.11.1", "tsx": "4.17.0", "typescript": "5.5.4", "unplugin-auto-import": "^0.18.2", "unplugin-icons": "0.19.2", "unplugin-vue-components": "0.27.4", "vite": "5.4.1", "vite-plugin-progress": "0.0.7", "vite-plugin-pwa": "^0.21.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.3.8", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.0.29", "workbox-window": "^7.3.0"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "website": "https://admin.soybeanjs.cn"}