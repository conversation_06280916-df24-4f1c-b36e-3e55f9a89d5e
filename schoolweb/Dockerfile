# 构建阶段
FROM hub.proxy.dmsy.me/library/node:20-alpine as builder

WORKDIR /app

# 全局安装 pnpm
RUN npm install -g pnpm@8.7.0

# 设置环境变量
ENV PNPM_NETWORK_TIMEOUT=300000

# 设置 npm 和 pnpm 使用淘宝源
RUN npm config set registry https://registry.npmmirror.com/ && \
    pnpm config set registry https://registry.npmmirror.com/

# 复制 package.json
COPY package*.json ./

# 安装所有依赖
RUN pnpm install --no-frozen-lockfile --shamefully-hoist

# 复制源代码
COPY . .

# 使用 build:test 脚本进行构建
RUN pnpm run build:test

# 运行阶段
FROM hub.proxy.dmsy.me/library/nginx:alpine

# 复制 nginx 配置文件
COPY nginx.conf /etc/nginx/templates/default.conf.template

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 设置环境变量默认值
ENV DOMAIN=localhost

# 使用模板系统替换环境变量
CMD ["nginx", "-g", "daemon off;"]
