import { computed } from 'vue';
import { useCountDown, useLoading } from '@sa/hooks';
import { $t } from '@/locales';
import { REG_PHONE } from '@/constants/reg';
import { sendEmailCaptcha } from '@/service/api/user';

export function useCaptcha() {
  const { loading, startLoading, endLoading } = useLoading();
  const { count, start, stop, isCounting } = useCountDown(60);

  const label = computed(() => {
    let text = $t('page.login.codeLogin.getCode');

    const countingLabel = $t('page.login.codeLogin.reGetCode', { time: count.value });

    if (loading.value) {
      text = '';
    }

    if (isCounting.value) {
      text = countingLabel;
    }

    return text;
  });

  function isPhoneValid(phone: string) {
    if (phone.trim() === '') {
      window.$message?.error?.($t('form.phone.required'));

      return false;
    }

    if (!REG_PHONE.test(phone)) {
      window.$message?.error?.($t('form.phone.invalid'));

      return false;
    }

    return true;
  }

  function isEmailValid(email: string) {
    if (email.trim() === '') {
      window.$message?.error?.('请输入邮箱地址');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      window.$message?.error?.('请输入正确的邮箱格式');
      return false;
    }

    return true;
  }

  async function getCaptcha(phoneOrEmail: string) {
    const isEmail = phoneOrEmail.includes('@');
    const valid = isEmail ? isEmailValid(phoneOrEmail) : isPhoneValid(phoneOrEmail);

    if (!valid || loading.value) {
      return;
    }

    startLoading();

    try {
      if (isEmail) {
        await sendEmailCaptcha(phoneOrEmail);
        window.$message?.success?.('验证码已发送至您的邮箱，请注意查收');
      } else {
        await new Promise(resolve => {
          setTimeout(resolve, 500);
        });
        window.$message?.success?.($t('page.login.codeLogin.sendCodeSuccess'));
      }

      start();
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      window.$message?.error?.('发送验证码失败，请稍后重试');
    } finally {
      endLoading();
    }
  }

  return {
    label,
    start,
    stop,
    isCounting,
    loading,
    getCaptcha
  };
}
