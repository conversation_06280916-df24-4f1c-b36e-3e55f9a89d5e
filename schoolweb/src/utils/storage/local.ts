import type { <PERSON><PERSON><PERSON> } from '@/typings/union-key';
import type { Api } from '@/typings/api';
import type { App } from '@/typings/app';

export interface Local {
  /** 主题颜色 */
  themeColor: string;
  /** 主题配置 */
  themeSetting: {
    /** 布局样式 */
    layout: UnionKey.ThemeLayoutMode;
    /** 侧边栏折叠状态 */
    siderCollapse: boolean;
  };
  /** 多页签路由信息 */
  multiTabRoutes: App.Global.TabRoute[];
  /** 用户token */
  token: string;
  /** token名称 */
  tokenName: string;
  /** token值 */
  tokenValue: string;
  /** 用户信息 */
  userInfo: Api.Auth.UserInfo;
  /** 浏览器指纹 */
  browserFingerprint?: string;
} 