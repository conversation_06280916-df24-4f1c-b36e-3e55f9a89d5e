/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      tokenValue: string;
      tokenName: string;
      // refreshToken: string;
    }
    interface tokenInfo{
      isLogin: boolean;
      loginDevice: string;
      loginId: string;
      loginType: string;
      sessionTimeout: number;
      tag: null;
      tokenActiveTimeout: number;
      tokenName: string;
      tokenSessionTimeout: number;
      tokenTimeout: number;
      tokenValue: string;
      [property: string]: any;
    }
    interface LoginInfos {
      tokenInfo: tokenInfo;
    }
    interface UserInfo {
      userId: string;
      name: string;
      stuId: string;
      cardId: string;
      academy: string;
      email: string;
      avatar: string;
      cardPhoto: string;
      status: number; // 用户状态：1-正常，2-待认证，3-已禁用，4-已注销
      userType: number; // 用户类型：1-学生，2-教师
      roles: string[];
      buttons: string[];
    }

    interface LoginParams {
      /** 用户名/邮箱 */
      username: string;
      /** 密码 */
      password: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  interface SuccessResponse<T> {
    /** Business code */
    code: string | number;
    /** Business data */
    data: T;
    /** Business message */
    msg: string;
  }

  interface FailResponse {
    /** Business code */
    code: string | number;
    /** Business message */
    msg: string;
  }

  namespace Demo {
    interface Result<T> {
      status: string;
      message: string;
      result: T;
    }
  }
}
