import {computed, Reactive, reactive, ref} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {defineStore} from 'pinia';
import {useLoading} from '@sa/hooks';
import {SetupStoreId} from '@/enum';
import {useRouterPush} from '@/hooks/common/router';
import {fetchGetUserInfo, fetchLogin} from '@/service/api';
import {localStg} from '@/utils/storage';
import {$t} from '@/locales';
import {useRouteStore} from '../route';
import {useTabStore} from '../tab';
import {clearAuthStorage, getToken} from './shared';
import {fetchLoginWithCode, type LoginResponse, verifyUserIdentity} from '@/service/api/user';

// 定义 Local 接口扩展
declare module '@/utils/storage' {
  interface Local {
    token: string;
    refreshToken: string;
    tokenName: string;
    tokenValue: string;
  }
}

// 定义角色枚举
export enum UserRole {
  SUPER_ADMIN = 'superAdmin',
  ADMIN = 'admin',
  TEACHER = 'teacher',
  STUDENT = 'student'
}

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
    const route = useRoute();
    const router = useRouter();
    const routeStore = useRouteStore();
    const tabStore = useTabStore();
    const {toLogin, redirectFromLogin} = useRouterPush(false);
    const {loading: loginLoading, startLoading, endLoading} = useLoading();

    const token = ref('');
    const isUserInfoInitialized = ref(false);

    const userInfo = ref<Api.Auth.UserInfo>({
      userId: '',
      name: '',
      stuId: '',
      cardId: '',
      academy: '',
      email: '',
      avatar: '',
      cardPhoto: '',
      status: 0, // 0-未认证
      userType: 1, // 默认为学生
      roles: [],
      buttons: []
    });

    /** is super role in static route */
    const isStaticSuper = computed(() => {
      const {VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE} = import.meta.env;
      return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.value.roles.includes(VITE_STATIC_SUPER_ROLE);
    });

    /** 角色判断计算属性 */
    const isSuperAdmin = computed(() => userInfo.value.roles.includes(UserRole.SUPER_ADMIN));
    const isAdmin = computed(() => userInfo.value.roles.includes(UserRole.ADMIN) || isSuperAdmin.value);
    const isTeacher = computed(() => userInfo.value.roles.includes(UserRole.TEACHER));
    const isStudent = computed(() => userInfo.value.roles.includes(UserRole.STUDENT));

    /** 权限判断方法 */
    const hasRole = (role: UserRole | UserRole[]) => {
      const roles = Array.isArray(role) ? role : [role];
      return roles.some(r => userInfo.value.roles.includes(r));
    };

    /** 按钮权限判断方法 */
    const hasButton = (buttonCode: string) => {
      return userInfo.value.buttons.includes(buttonCode);
    };

    /** Is login */
    const isLogin = computed(() => {
      const hasToken = Boolean(localStg.get('token'));
      console.log('[Auth Store] 检查登录状态', {hasToken, tokenValue: token.value});
      return Boolean(hasToken);
    });

    /** Reset auth store */
    async function resetStore() {
      console.log('[Auth Store] 重置store');
      clearAuthStorage();
      isUserInfoInitialized.value = false;
      token.value = '';
      userInfo.value = {
        userId: '',
        name: '',
        stuId: '',
        cardId: '',
        academy: '',
        email: '',
        avatar: '',
        cardPhoto: '',
        status: 0,
        userType: 1,
        roles: [],
        buttons: []
      };

      if (!route.meta.constant) {
        await toLogin();
      }

      tabStore.cacheTabs();
      routeStore.resetStore();
    }

    /**
     * Login
     *
     * @param email email
     * @param password Password
     * @param [redirect=true] Whether to redirect after login. Default is `true`
     */
    async function login(email: string, password: string, redirect = true) {
      startLoading();
      console.log('[Auth] 开始登录流程', {email});
      try {
        // 调用登录API
        // @ts-ignore - 类型不匹配，暂时忽略
        const response = await fetchLogin(email, password);
        console.log('[Auth] 登录API响应', response);


        // 提取登录信息
        const loginData = response;
        console.log('[Auth] 登录数据', loginData);

        // 确保tokenInfo存在
        if (!loginData.tokenInfo) {
          window.$message?.error('登录失败: 无效的Token信息');
          return null;
        }

        // 存储token信息
        const tokenInfo = loginData.tokenInfo;
        localStg.set('token', tokenInfo.tokenValue);
        localStg.set('tokenName', tokenInfo.tokenName);
        localStg.set('tokenValue', tokenInfo.tokenValue);
        token.value = tokenInfo.tokenValue;

        token.value = tokenInfo.tokenValue;

        // 获取用户信息
        const userInfoSuccess = await getUserInfo();
        if (!userInfoSuccess) {
          window.$message?.error('登录失败: 无法获取用户信息');
          return null;
        }

        // 初始化路由
        await routeStore.initAuthRoute();

        // 处理重定向
        if (redirect) {
          window.$notification?.success({
            title: $t('page.login.common.loginSuccess'),
            content: $t('page.login.common.welcomeBack', {userName: userInfo.value.name}),
            duration: 4500
          });

          await redirectFromLogin();
        }

        // 返回登录数据
        return loginData;
      } catch (error) {
        console.error('[Auth] 登录失败:', error);
        window.$message?.error('登录失败');
        resetStore();
        return null;
      } finally {
        endLoading();
      }
    }

    async function loginByToken(tokenInfo: any) {
      console.log('[Auth] 开始Token登录', {tokenInfo});
      try {
        // 检查tokenInfo是否存在
        if (!tokenInfo || !tokenInfo.tokenValue) {
          console.error('[Auth] Token信息无效');
          return false;
        }

        // 1. 存储 token 信息
        localStg.set('token', tokenInfo.tokenValue);
        localStg.set('tokenName', tokenInfo.tokenName);
        localStg.set('tokenValue', tokenInfo.tokenValue);
        token.value = tokenInfo.tokenValue;
        console.log('[Auth] Token信息已存储');

        // 2. 获取用户信息并等待完成
        const pass = await getUserInfo();
        console.log('[Auth] 获取用户信息结果', {pass, userInfo: userInfo.value});
        if (!pass) {
          console.error('[Auth] 获取用户信息失败');
          return false;
        }
        return true;
      } catch (error) {
        console.error('[Auth] Token登录失败:', error);
        return false;
      }
    }

    async function getUserInfo() {
      console.log('[Auth] 开始获取用户信息');
      const {data} = await fetchGetUserInfo();
      console.log('[Auth] 用户信息API响应', {data});
      if (data) {
        userInfo.value = data;
        isUserInfoInitialized.value = true;
        return true;
      }
      isUserInfoInitialized.value = false;
      return false;
    }

    async function initUserInfo() {
      console.log('[Auth Store] 开始初始化用户信息');
      const storedToken = getToken();
      console.log('[Auth Store] Token检查', {storedToken});

      if (storedToken) {
        token.value = storedToken;
        const pass = await getUserInfo();
        console.log('[Auth Store] 初始化用户信息结果', {pass, userInfo: userInfo.value});

        if (!pass) {
          console.log('[Auth Store] 初始化失败，重置store');
          await resetStore();
          return false;
        }
        return true;
      }
      return false;
    }

    /**
     * Login with email code
     *
     * @param email email
     * @param code verification code
     * @param [redirect=true] Whether to redirect after login. Default is `true`
     */
    async function loginWithCode(email: string, code: string, redirect = true) {
      startLoading();
      console.log('[Auth] 开始邮箱验证码登录流程', {email, code});
      try {
        // 调用验证码登录API
        const response = await fetchLoginWithCode(email, code);
        console.log('[Auth] 邮箱验证码登录API响应', response);

        // 解析响应数据
        if (!response || !response.data) {
          window.$message?.error('登录失败: 无效的响应数据');
          return null;
        }

        // 提取登录信息
        const loginData = response.data;
        console.log('[Auth] 登录数据', loginData);

        // 确保tokenInfo存在
        if (!loginData.tokenInfo) {
          window.$message?.error('登录失败: 无效的Token信息');
          return null;
        }

        // 存储token信息
        const tokenInfo = loginData.tokenInfo;
        localStg.set('token', tokenInfo.tokenValue);
        localStg.set('tokenName', tokenInfo.tokenName);
        localStg.set('tokenValue', tokenInfo.tokenValue);
        token.value = tokenInfo.tokenValue;

        // 获取用户信息
        const userInfoSuccess = await getUserInfo();
        if (!userInfoSuccess) {
          window.$message?.error('登录失败: 无法获取用户信息');
          return null;
        }

        // 初始化路由
        await routeStore.initAuthRoute();

        // 处理重定向
        if (redirect) {
          window.$notification?.success({
            title: $t('page.login.common.loginSuccess'),
            content: $t('page.login.common.welcomeBack', {userName: userInfo.value.name}),
            duration: 4500
          });

          await redirectFromLogin();
        }

        // 返回登录数据
        return {
          tokenInfo
        };
      } catch (error) {
        console.error('[Auth] 邮箱验证码登录失败:', error);
        window.$message?.error('登录失败');
        resetStore();
        return null;
      } finally {
        endLoading();
      }
    }

    /**
     * 提交身份验证信息
     */
    async function verifyIdentity(verifyData: {
      name: string;
      stuId: string;
      cardId: string;
      academy: string;
      userType: number;
      cardPhoto: string;
    }) {
      try {
        await verifyUserIdentity(verifyData);
        // 重新获取用户信息以更新状态
        await getUserInfo();
        return true;
      } catch (error) {
        console.error('[Auth] 身份认证失败:', error);
        return false;
      }
    }

    // 在创建store时自动初始化
    if (getToken()) {
      console.log('[Auth Store] 检测到token，自动初始化');
      initUserInfo();
    }

    return {
      token,
      userInfo,
      isUserInfoInitialized,
      isStaticSuper,
      isLogin,
      loginLoading,
      // 导出角色判断
      isSuperAdmin,
      isAdmin,
      isTeacher,
      isStudent,
      hasRole,
      hasButton,
      resetStore,
      login,
      loginWithCode,
      verifyIdentity,
      initUserInfo
    };
  })
;
