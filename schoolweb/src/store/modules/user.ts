import { defineStore } from 'pinia';
import { ref } from 'vue';
import { fetchUserInfo, updateUser, verifyUserIdentity } from '@/service/api/user';
import type { UserInfo } from '@/service/api/user';

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null);
  
  // 加载状态
  const loading = ref(false);
  
  // 获取用户信息
  async function getUserInfo() {
    try {
      loading.value = true;
      const response = await fetchUserInfo();
      if (response.data) {
        userInfo.value = response.data;
        return true;
      }
      return false;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 更新用户信息
  async function updateUserInfo(userData: Partial<UserInfo>) {
    try {
      loading.value = true;
      await updateUser(userData);
      // 更新本地存储的用户信息
      if (userInfo.value) {
        userInfo.value = { ...userInfo.value, ...userData };
      }
      return true;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 提交身份认证
  async function submitIdentityVerification(verifyData: {
    name: string;
    stuId: string;
    cardId: string;
    academy: string;
    userType: number;
    cardPhoto: string;
  }) {
    try {
      loading.value = true;
      await verifyUserIdentity(verifyData);
      // 重新获取用户信息以更新状态
      await getUserInfo();
      return true;
    } catch (error) {
      console.error('提交身份认证失败:', error);
      return false;
    } finally {
      loading.value = false;
    }
  }
  
  // 重置Store
  function resetStore() {
    userInfo.value = null;
  }
  
  return {
    userInfo,
    loading,
    getUserInfo,
    updateUserInfo,
    submitIdentityVerification,
    resetStore
  };
}); 