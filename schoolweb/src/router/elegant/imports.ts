/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  home: () => import("@/views/home/<USER>"),
  logmanagement: () => import("@/views/logManagement/index.vue"),
  order: () => import("@/views/order/index.vue"),
  profile: () => import("@/views/profile/index.vue"),
  rolemanagement: () => import("@/views/roleManagement/index.vue"),
  "skill-detail": () => import("@/views/skill-detail/index.vue"),
  "skill-publish": () => import("@/views/skill-publish/index.vue"),
  skill: () => import("@/views/skill/index.vue"),
  social: () => import("@/views/social/index.vue"),
  "user-credit": () => import("@/views/user-credit/index.vue"),
  "user-profile": () => import("@/views/user-profile/index.vue"),
  usermanagement: () => import("@/views/userManagement/index.vue"),
  wallet: () => import("@/views/wallet/index.vue"),
};
