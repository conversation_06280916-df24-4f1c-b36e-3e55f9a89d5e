import type {
  LocationQueryRaw,
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteLocationRaw,
  Router
} from 'vue-router';
import type { RouteKey, RoutePath } from '@elegant-router/types';
import { getRouteName } from '@/router/elegant/transform';
import { useAuthStore } from '@/store/modules/auth';
import { useRouteStore } from '@/store/modules/route';
import { localStg } from '@/utils/storage';

/**
 * create route guard
 *
 * @param router router instance
 */
export function createRouteGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    console.log('[Route Guard] 路由拦截开始', { to: to.path, from: from.path });
    
    const authStore = useAuthStore();
    const routeStore = useRouteStore();

    // 如果有token但用户信息未初始化，先初始化用户信息
    const hasToken = Boolean(localStg.get('token'));
    if (hasToken && !authStore.isUserInfoInitialized) {
      console.log('[Route Guard] 检测到token但用户信息未初始化，开始初始化');
      await authStore.initUserInfo();
    }
    
    const location = await initRoute(to);
    console.log('[Route Guard] 路由初始化结果', { location });

    if (location) {
      next(location);
      return;
    }

    const rootRoute: RouteKey = 'root';
    const loginRoute: RouteKey = 'login';
    const noAuthorizationRoute: RouteKey = '403';

    const isLogin = Boolean(localStg.get('token'));
    const needLogin = !to.meta.constant;
    const routeRoles = to.meta.roles || [];
    console.log('[Route Guard] 路由状态', { 
      isLogin, 
      needLogin, 
      routeRoles,
      userRoles: authStore.userInfo.roles,
      toPath: to.path,
      toName: to.name,
      isUserInfoInitialized: authStore.isUserInfoInitialized
    });

    const hasRole = authStore.userInfo.roles.some(role => routeRoles.includes(role));
    const hasAuth = authStore.isStaticSuper || !routeRoles.length || hasRole;
    console.log('[Route Guard] 权限检查', { hasRole, hasAuth });

    const routeSwitches: CommonType.StrategicPattern[] = [
      // if it is login route when logged in, then switch to the root page
      {
        condition: isLogin && to.name === loginRoute,
        callback: () => {
          next({ name: rootRoute });
        }
      },
      // if it is constant route, then it is allowed to access directly
      {
        condition: !needLogin,
        callback: () => {
          handleRouteSwitch(to, from, next);
        }
      },
      // if the route need login but the user is not logged in, then switch to the login page
      {
        condition: !isLogin && needLogin,
        callback: () => {
          next({ name: loginRoute, query: { redirect: to.fullPath } });
        }
      },
      // if the route has specific roles requirement and user doesn't have permission, redirect to 403 instead of trying to access
      {
        condition: isLogin && needLogin && routeRoles.length > 0 && !hasAuth,
        callback: () => {
          console.log('[Route Guard] 用户无权访问此路由', { routeRoles, userRoles: authStore.userInfo.roles });
          next({ name: noAuthorizationRoute });
        }
      },
      // if the user is logged in and has authorization, then it is allowed to access
      {
        condition: isLogin && needLogin && hasAuth,
        callback: () => {
          handleRouteSwitch(to, from, next);
        }
      }
    ];

    routeSwitches.some(({ condition, callback }) => {
      if (condition) {
        callback();
      }

      return condition;
    });
  });
}

/**
 * initialize route
 *
 * @param to to route
 */
async function initRoute(to: RouteLocationNormalized): Promise<RouteLocationRaw | null> {
  console.log('[Route Guard] 开始初始化路由', { to: to.path });
  const authStore = useAuthStore();
  const routeStore = useRouteStore();

  const notFoundRoute: RouteKey = 'not-found';
  const isNotFoundRoute = to.name === notFoundRoute;

  // 对于身份认证页面特殊处理
  if (to.path === '/profile/identity') {
    console.log('[Route Guard] 访问身份认证页面');
    return null;
  }

  // if the constant route is not initialized, then initialize the constant route
  if (!routeStore.isInitConstantRoute) {
    console.log('[Route Guard] 初始化常量路由');
    await routeStore.initConstantRoute();

    // the route is captured by the "not-found" route because the constant route is not initialized
    // after the constant route is initialized, redirect to the original route
    if (isNotFoundRoute) {
      const path = to.fullPath;
      console.log('[Route Guard] 重定向到原始路由', { path });

      const location: RouteLocationRaw = {
        path,
        replace: true,
        query: to.query,
        hash: to.hash
      };

      return location;
    }
  }

  // if the route is the constant route but is not the "not-found" route, then it is allowed to access.
  if (to.meta.constant && !isNotFoundRoute) {
    console.log('[Route Guard] 访问常量路由');
    return null;
  }

  // the auth route is initialized
  // it is not the "not-found" route, then it is allowed to access
  if (routeStore.isInitAuthRoute && !isNotFoundRoute) {
    console.log('[Route Guard] 访问已初始化的认证路由');
    return null;
  }

  // if the auth route is not initialized, then initialize the auth route
  const isLogin = Boolean(localStg.get('token'));
  console.log('[Route Guard] 登录状态检查', { 
    isLogin, 
    token: localStg.get('token'),
    isUserInfoInitialized: authStore.isUserInfoInitialized 
  });
  
  // initialize the auth route requires the user to be logged in, if not, redirect to the login page
  if (!isLogin) {
    console.log('[Route Guard] 未登录，重定向到登录页');
    const loginRoute: RouteKey = 'login';
    const query = getRouteQueryOfLoginRoute(to, routeStore.routeHome);

    const location: RouteLocationRaw = {
      name: loginRoute,
      query
    };

    return location;
  }

  // 确保用户信息已初始化
  if (!authStore.isUserInfoInitialized) {
    console.log('[Route Guard] 初始化用户信息');
    await authStore.initUserInfo();
  }

  console.log('[Route Guard] 开始初始化认证路由');
  await routeStore.initAuthRoute();

  // the route is captured by the "not-found" route because the auth route is not initialized
  // after the auth route is initialized, redirect to the original route
  if (isNotFoundRoute) {
    const rootRoute: RouteKey = 'root';
    const path = to.redirectedFrom?.name === rootRoute ? '/' : to.fullPath;

    const location: RouteLocationRaw = {
      path,
      replace: true,
      query: to.query,
      hash: to.hash
    };

    return location;
  }

  return null;
}

function handleRouteSwitch(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
  // route with href
  if (to.meta.href) {
    window.open(to.meta.href, '_blank');

    next({ path: from.fullPath, replace: true, query: from.query, hash: to.hash });

    return;
  }

  next();
}

function getRouteQueryOfLoginRoute(to: RouteLocationNormalized, routeHome: RouteKey) {
  const loginRoute: RouteKey = 'login';
  const redirect = to.fullPath;
  const [redirectPath, redirectQuery] = redirect.split('?');
  const redirectName = getRouteName(redirectPath as RoutePath);

  const isRedirectHome = routeHome === redirectName;

  const query: LocationQueryRaw = to.name !== loginRoute && !isRedirectHome ? { redirect } : {};

  if (isRedirectHome && redirectQuery) {
    query.redirect = `/?${redirectQuery}`;
  }

  return query;
}
