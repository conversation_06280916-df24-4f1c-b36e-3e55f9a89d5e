import type { CustomRoute } from '@elegant-router/types';

export const userRoutes: CustomRoute[] = [
  {
    name: 'login',
    path: '/login',
    component: 'layout.blank$view.login',
    meta: {
      title: '登录',
      constant: true
    }
  },
  {
    name: 'register',
    path: '/register',
    component: 'layout.blank$view.register',
    meta: {
      title: '注册',
      constant: true
    }
  },
  {
    name: 'user-profile',
    path: '/user/profile',
    component: 'layout.basic$view.user-profile',
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    name: 'user-credit',
    path: '/user/credit',
    component: 'layout.basic$view.user-credit',
    meta: {
      title: '信用评级',
      requiresAuth: true
    }
  }
]; 