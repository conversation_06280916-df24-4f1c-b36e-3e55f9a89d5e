import { request } from '../request';
import type { ApiResponse } from './user';

export interface RoleInfo {
  roleId: number;
  roleName: string;
  description: string;
  createdTime: number | null;
  updatedTime: number | null;
}

/**
 * 获取角色列表
 */
export function getRoleList(): Promise<RoleInfo[]> {
  return request<ApiResponse<RoleInfo[]>>({
    url: '/roles',
    method: 'get'
  }).then(res => {
    const data = res?.data;
    return Array.isArray(data) ? data : [];
  });
}

/**
 * 创建新角色
 */
export function createRole(data: Partial<RoleInfo>) {
  return request<ApiResponse<boolean>>({
    url: '/roles',
    method: 'post',
    data
  }).then(res => res.data);
}

/**
 * 更新角色信息
 */
export function updateRole(roleId: number, data: Partial<RoleInfo>) {
  return request<ApiResponse<boolean>>({
    url: `/roles/${roleId}`,
    method: 'put',
    data
  }).then(res => res.data);
}

/**
 * 删除角色
 */
export function deleteRole(roleId: number) {
  return request<ApiResponse<boolean>>({
    url: `/roles/${roleId}`,
    method: 'delete'
  }).then(res => res.data);
}

/**
 * 批量为角色添加用户
 * @param roleId 角色ID
 * @param userIds 用户ID列表
 */
export function addUsersToRole(roleId: number, userIds: number[]) {
  return request<ApiResponse<boolean>>({
    url: `/roles/${roleId}/users`,
    method: 'post',
    data: userIds
  });
  return response;
}

/**
 * 从角色中移除指定用户
 * @param userIds 用户ID列表
 * @param roleId 角色ID
 * @returns 是否移除成功
 */
export const removeUsersFromRole = async (userIds: number[], roleId: number): Promise<{ userId: string; name: string; stuId: string }[]> => {
  const response = await request<ApiResponse<null>>({
    url: `/roles/${roleId}/users`,
    method: 'delete',
    data: userIds
  });
  return response;
};

/**
 * 删除用户的指定角色
 * @param userId 用户ID
 * @param roleIds 角色ID列表
 */
export function deleteUserRoles(userId: number, roleIds: number[]) {
  return request<ApiResponse<boolean>>({
    url: `/roles/users/${userId}/roles`,
    method: 'delete',
    data: roleIds
  }).then(res => res.data);
}

/**
 * 获取角色下的用户列表
 * @param roleId 角色ID
 */
export function getRoleUsers(roleId: number): Promise<{ userId: string; name: string; stuId: string }[]> {
  return request<ApiResponse<{ userId: string; name: string; stuId: string }[]>>({
    url: `/roles/${roleId}/users`,
    method: 'get'
  }).then(res => {
    const data = res?.data;
    return Array.isArray(data) ? data : [];
  });
} 