import { request } from '../request';
import type { ApiResponse } from '../types';

export interface CreditScore {
  userId: number;
  score: number;
  level: string; // A+, A, B+, B, C, D
  levelDescription: string;
  lastUpdatedTime: string;
}

export interface CreditRecord {
  id: number;
  userId: number;
  changeType: number; // 0-初始化，1-订单完成，2-被评价，3-评价他人，4-取消订单，5-拒绝订单，6-违规
  changeScore: number; // 可为正可为负
  remark: string;
  relatedId?: number; // 关联订单ID
  createdTime: string;
}

export interface CreditRecordSearchParams {
  changeType?: number;
  startTime?: string;
  endTime?: string;
  page: number;
  size: number;
}

export interface CreditRecordListResult {
  records: CreditRecord[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 获取用户信用评分
 */
export function fetchCreditScore(userId?: number) {
  return request<ApiResponse<CreditScore>>({
    url: '/credit/score',
    method: 'get',
    params: { userId }
  });
}

/**
 * 获取用户信用记录
 */
export function fetchCreditRecords(params: CreditRecordSearchParams) {
  return request<ApiResponse<CreditRecordListResult>>({
    url: '/credit/records',
    method: 'get',
    params
  });
}

/**
 * 获取用户信用规则说明
 */
export function fetchCreditRules() {
  return request<ApiResponse<{
    levels: { level: string; min: number; max: number; description: string }[];
    rules: { type: number; name: string; score: number; description: string }[];
  }>>({
    url: '/credit/rules',
    method: 'get'
  });
}

/**
 * 管理员调整用户信用分
 */
export function adjustCreditScore(userId: number, data: {
  score: number;
  remark: string;
}) {
  return request<ApiResponse<string>>({
    url: `/credit/adjust/${userId}`,
    method: 'post',
    data
  });
} 