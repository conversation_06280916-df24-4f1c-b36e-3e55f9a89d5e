import { request } from '../request';
import type { ApiResponse } from '../types';

export interface SkillCategory {
  categoryId: number;
  name: string;
  description?: string;
  icon?: string;
  sortOrder?: number;
  status?: number;
}

export interface SkillTag {
  id: number;
  name: string;
}

export interface SkillService {
  id: number;
  title: string;
  description: string;
  price: number;
  unit: string; // 计价单位（小时/次/天等）
  categoryId: number;
  categoryName?: string;
  tags: string[] | number[]; // 可以是标签ID或标签名称
  providerId: number;
  providerName?: string;
  providerAvatar?: string;
  rating: number;
  reviewCount: number;
  status: number; // 0-草稿，1-审核中，2-已发布，3-已下架
  createdTime: string;
  updatedTime: string;
  coverImage?: string;
  images?: string[];
}

export interface SkillSearchParams {
  keyword?: string;
  categoryId?: number;
  priceMin?: number;
  priceMax?: number;
  page: number;
  size: number;
  sortBy?: string;
}

export interface SkillListResult {
  records: SkillService[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 获取技能分类列表
 */
export function fetchSkillCategories() {
  return request<SkillCategory[]>({
    url: '/skill/categories',
    method: 'get'
  });
}

/**
 * 获取技能服务列表
 */
export function fetchSkillList(params: SkillSearchParams) {
  return request<SkillListResult>({
    url: '/skill/list',
    method: 'get',
    params
  }).then(res => res.data);
}

/**
 * 获取技能服务详情
 */
export function fetchSkillDetail(serviceId: number) {
  return request<ApiResponse<SkillService>>({
    url: `/skill/${serviceId}`,
    method: 'get'
  });
}

/**
 * 发布技能服务
 */
export function publishSkill(data: any) {
  return request<{ serviceId: number }>({
    url: '/skill/publish',
    method: 'post',
    data
  }).then(res => res.data);
}

/**
 * 更新技能服务
 */
export function updateSkill(serviceId: number, data: any) {
  return request<ApiResponse<string>>({
    url: `/skill/${serviceId}`,
    method: 'put',
    data
  });
}

/**
 * 删除技能服务
 */
export function deleteSkill(serviceId: number) {
  return request<ApiResponse<string>>({
    url: `/skill/${serviceId}`,
    method: 'delete'
  });
}

/**
 * 获取我发布的服务
 */
export function fetchMySkills(params: {
  page: number;
  size: number;
  status?: number;
}) {
  return request<ApiResponse<SkillListResult>>({
    url: '/skill/my',
    method: 'get',
    params
  });
}

// 保留一些兼容性API
export function createSkill(data: Partial<SkillService>) {
  return request<ApiResponse<number>>({
    url: '/skill/create',
    method: 'post',
    data
  });
}

export function fetchSkillTags(categoryId?: number) {
  return request<string[]>({
    url: '/skill/tags',
    method: 'get',
    params: { categoryId }
  });
}

export function fetchUserSkills(userId: number) {
  return request<SkillService[]>({
    url: '/skill/user',
    method: 'get',
    params: { userId }
  });
}

export function changeSkillStatus(id: number, status: number) {
  return request<ApiResponse<void>>({
    url: `/skill/${id}/status`,
    method: 'put',
    data: { status }
  });
} 