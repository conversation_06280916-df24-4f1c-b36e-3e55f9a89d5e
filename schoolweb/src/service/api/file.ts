import { request } from '../request';
import type { ApiResponse } from './user';
import { localStg } from '@/utils/storage';
import { FilePurpose } from '@/enums/filePurpose';

/**
 * 上传文件
 * @param file 文件对象
 * @param purpose 文件用途
 */
export function uploadFile(file: File, purpose: FilePurpose = FilePurpose.OTHER) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('purpose', purpose);
  return request<string>({
    url: '/upload',
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  }).then(res => res.data);
}

/**
 * 获取文件URL
 * @param fileUrl 文件路径
 */
export function getFileUrl(fileUrl: string) {
  if (!fileUrl) return '';
  // 如果已经是完整URL，直接返回
  if (fileUrl.startsWith('http')) {
    return fileUrl;
  }
  const baseUrl = import.meta.env.VITE_SERVICE_BASE_URL;
  // 确保路径以/开头
  const path = fileUrl.startsWith('/') ? fileUrl : `/${fileUrl}`;
  // 文件访问不需要token，因为已经在后端配置了白名单
  return `${baseUrl}${path}`;
}