import { request } from '../request';
import type { ApiResponse } from '../types';

export interface UserInfo {
  userId: number;
  name: string;
  stuId: string;
  cardId: string;  // 一卡通号
  academy: string; // 学院
  email: string;
  roles: string[];
  permissions: string[];
  status: number;  // 用户状态：1-正常，2-待审核，3-已禁用，4-已注销，5-待上传证件
  statusReason?: string; // 状态变更原因
  userType: number; // 用户类型：1-学生，2-教师
  labStatus: number;  // 实验室状态：1-正常，2-待认证，3-已开除，4-已退出，5-已毕业
  createdTime: string | null;
  updatedTime: string | null;
  avatar: string;  // 用户头像URL
  cardPhoto: string; // 一卡通照片URL
  password?: string; // 可选的密码字段，仅用于更新
}

export interface PageResult<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

// 登录响应类型
export interface LoginResponse {
  tokenInfo: {
    tokenName: string;
    tokenValue: string;
    isLogin: boolean;
    loginId: string;
    loginType: string;
    tokenTimeout: number;
    sessionTimeout: number;
    tokenSessionTimeout: number;
    tokenActiveTimeout: number;
    loginDevice: string;
    tag: any;
  };
  userInfo?: UserInfo;
}

/**
 * 用户登录
 */
export function fetchLogin(email: string, password: string) {
  return request<ApiResponse<LoginResponse>>({
    url: '/user/login',
    method: 'post',
    data: { email, password }
  }).then(res => res.data);
}

/**
 * 用户登出
 */
export function fetchLogout() {
  return request<ApiResponse<string>>({
    url: '/user/logout',
    method: 'post'
  });
}

/**
 * 检查登录状态
 */
export function fetchCheckLogin() {
  return request<ApiResponse<any>>({
    url: '/user/check-login',
    method: 'get'
  });
}

/**
 * 获取当前用户信息
 */
export function fetchUserInfo() {
  return request<ApiResponse<UserInfo>>({
    url: '/user/info',
    method: 'get'
  });
}

/**
 * 修改密码
 */
export function updatePassword(oldPassword: string, newPassword: string) {
  return request<ApiResponse<string>>({
    url: '/user/password',
    method: 'put',
    data: { oldPassword, newPassword }
  });
}

/**
 * 搜索用户
 */
export function searchUsers(query: string) {
  return request<ApiResponse<UserInfo[]>>({
    url: '/user/search',
    method: 'get',
    params: { query }
  });
}

/**
 * 获取所有用户列表（管理员权限）
 */
export function fetchAllUsers(params: {
  page: number;
  size: number;
  query?: string;
  status?: number;
}) {
  return request<ApiResponse<PageResult<UserInfo>>>({
    url: '/admin/user/list',
    method: 'get',
    params
  });
}

/**
 * 更新用户信息（管理员/用户自己）
 */
export function updateUser(data: Partial<UserInfo>) {
  return request<ApiResponse<string>>({
    url: '/user/update',
    method: 'post',
    data
  });
}

/**
 * 删除用户
 */
export function deleteUser(userId: number) {
  return request<ApiResponse<void>>({
    url: `/user/${userId}`,
    method: 'delete'
  }).then(res => res.data);
}

/**
 * 重置用户密码
 */
export function resetUserPassword(userId: number) {
  return request<ApiResponse<string>>({
    url: `/user/${userId}/reset-password`,
    method: 'post'
  }).then(res => res.data);
}

/**
 * 修改用户角色
 */
export function updateUserRole(userId: number, role: string) {
  return request<ApiResponse<void>>({
    url: `/user/${userId}/role`,
    method: 'put',
    data: { role }
  }).then(res => res.data);
}

interface UserBasicInfo {
  userId: string;
  name: string;
  stuId: string;
}

/**
 * 批量获取用户基本信息
 * @param userIds 用户ID列表
 */
export const batchGetUserInfo = (userIds: string[]): Promise<UserBasicInfo[]> => {
  return request<UserBasicInfo[]>({
    url: '/admin/user/batch-info',
    method: 'post',
    data: userIds
  }).then(res => {
    console.log('API Response:', res);
    if (res?.data) {
      return res.data;
    }
    console.warn('Invalid API response:', res);
    return [];
  });
};

/**
 * 上传文件
 */
export function uploadFile(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return request<ApiResponse<string>>({
    url: '/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  }).then(res => res.data);
}

/**
 * 通过Excel导入用户
 */
export function createUserByExcel(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return request<string>({
    url: '/admin/user/caeateUserByExcel',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  }).then(res => res.data);
}

/**
 * 获取所有可用的角色类型
 */
export function fetchAllRoles() {
  return request<ApiResponse<string[]>>({
    url: '/admin/user/roles',
    method: 'get'
  }).then(res => res.data);
}

/**
 * 发送邮箱验证码
 */
export function sendEmailCaptcha(email: string) {
  return request<ApiResponse<string>>({
    url: '/user/send-email-code',
    method: 'post',
    data: { email }
  });
}

/**
 * 邮箱验证码登录
 */
export function fetchLoginWithCode(email: string, code: string) {
  return request<ApiResponse<LoginResponse>>({
    url: '/user/login-with-code',
    method: 'post',
    data: { email, code }
  });
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  stuId?: string;
  cardId?: string;
  academy?: string;
  verificationCode: string;
}

/**
 * 用户注册
 */
export function fetchRegister(data: {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  verificationCode: string;
}) {
  return request({
    url: '/user/register',
    method: 'post',
    data
  });
}

/**
 * 用户身份验证
 */
export function verifyUserIdentity(data: {
  name: string;
  stuId: string;
  cardId: string;
  academy: string;
  userType: number;
  cardPhoto: string;
}) {
  return request({
    url: '/user/verify-identity',
    method: 'post',
    data
  });
}

/**
 * 获取用户身份验证状态
 */
export function getUserVerificationStatus() {
  return request<ApiResponse<{status: number; statusText: string}>>({
    url: '/user/verify-status',
    method: 'get'
  });
}
