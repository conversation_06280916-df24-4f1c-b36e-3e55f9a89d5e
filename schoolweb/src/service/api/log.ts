import { request } from '../request';
import type { PageResult, ApiResponse } from './user';

export interface SystemLog {
  id: number;
  operationModule: string;
  operationType: string;
  operationDesc: string;
  requestUrl: string;
  requestMethod: string;
  requestParams: string;
  requestHeaders: string;
  operationIp: string;
  responseCode: number;
  responseMsg: string;
  responseData: string;
  userId: number;
  username: string;
  operationTime: string;
  executionTime: number;
}

export interface LogQueryParams {
  page: number;
  pageSize: number;
  startTime?: string;
  endTime?: string;
  userId?: string;
  username?: string;
  operationType?: string;
  operationModule?: string;
  responseCode?: string;
}

// 获取系统日志列表
export const fetchSystemLogs = async (params: LogQueryParams): Promise<PageResult<SystemLog>> => {
  const res = await request<ApiResponse<PageResult<SystemLog>>>({
    url: '/logs',
    method: 'GET',
    params
  });
  if (!res?.data) {
    return {
      records: [],
      total: 0,
      size: params.pageSize,
      current: params.page,
      pages: 0
    };
  }
  return res.data;
};

// 获取操作类型列表
export const fetchOperationTypes = async (): Promise<string[]> => {
  const res = await request<ApiResponse<string[]>>({
    url: '/logs/operation-types',
    method: 'GET'
  });
  return res?.data || [];
};

// 获取操作模块列表
export const fetchOperationModules = async (): Promise<string[]> => {
  const res = await request<ApiResponse<string[]>>({
    url: '/logs/operation-modules',
    method: 'GET'
  });
  return res?.data || [];
};

// 获取响应状态列表
export const fetchResponseCodes = async (): Promise<number[]> => {
  const res = await request<ApiResponse<number[]>>({
    url: '/logs/response-codes',
    method: 'GET'
  });
  return res?.data || [];
}; 