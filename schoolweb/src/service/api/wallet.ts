import { request } from '../request';
import type { ApiResponse } from '../types';

export interface Wallet {
  id: number;
  userId: number;
  balance: number;
  frozenAmount: number;
  updatedTime: string;
}

export interface Transaction {
  id: number;
  userId: number;
  amount: number;
  type: number; // 0-充值，1-消费，2-收入，3-提现，4-退款，5-冻结，6-解冻
  relatedId?: number; // 关联订单ID
  relatedType?: string; // 关联类型（order）
  status: number; // 0-处理中，1-成功，2-失败
  remark: string;
  createdTime: string;
  operatorId?: number; // 操作人ID（管理员）
}

export interface TransactionSearchParams {
  type?: number;
  status?: number;
  startTime?: string;
  endTime?: string;
  page: number;
  size: number;
}

export interface TransactionListResult {
  records: Transaction[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 获取钱包信息
 */
export function fetchWalletInfo() {
  return request<ApiResponse<Wallet>>({
    url: '/wallet/info',
    method: 'get'
  });
}

/**
 * 获取交易记录
 */
export function fetchTransactions(params: TransactionSearchParams) {
  return request<ApiResponse<TransactionListResult>>({
    url: '/wallet/transactions',
    method: 'get',
    params
  });
}

/**
 * 充值
 */
export function rechargeWallet(amount: number) {
  return request<ApiResponse<string>>({
    url: '/wallet/recharge',
    method: 'post',
    data: { amount }
  });
}

/**
 * 提现
 */
export function withdrawWallet(amount: number, accountInfo: string) {
  return request<ApiResponse<string>>({
    url: '/wallet/withdraw',
    method: 'post',
    data: { amount, accountInfo }
  });
}

/**
 * 冻结余额（用于订单支付）
 */
export function freezeBalance(amount: number, orderId: number) {
  return request<ApiResponse<string>>({
    url: '/wallet/freeze',
    method: 'post',
    data: { amount, orderId }
  });
}

/**
 * 解冻余额（取消订单时）
 */
export function unfreezeBalance(amount: number, orderId: number) {
  return request<ApiResponse<string>>({
    url: '/wallet/unfreeze',
    method: 'post',
    data: { amount, orderId }
  });
}

/**
 * 扣除余额（完成订单时）
 */
export function deductBalance(amount: number, orderId: number) {
  return request<ApiResponse<string>>({
    url: '/wallet/deduct',
    method: 'post',
    data: { amount, orderId }
  });
}

/**
 * 转账（服务费结算）
 */
export function transferToProvider(providerId: number, amount: number, orderId: number) {
  return request<ApiResponse<string>>({
    url: '/wallet/transfer',
    method: 'post',
    data: { providerId, amount, orderId }
  });
}

/**
 * 获取用户积分
 */
export function fetchUserCredits() {
  return request<{ credits: number }>({
    url: '/wallet/credits',
    method: 'get'
  });
}

/**
 * 积分兑换虚拟币
 */
export function exchangeCredits(credits: number) {
  return request<ApiResponse<{ amount: number }>>({
    url: '/wallet/exchange-credits',
    method: 'post',
    data: { credits }
  });
} 