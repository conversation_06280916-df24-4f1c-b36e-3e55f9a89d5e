import { request } from '../request';

export interface LoginResponse {
  isFirstLogin: boolean;
  tokenInfo: {
    tokenName: string;
    tokenValue: string;
    isLogin: boolean;
    loginId: string;
    loginType: string;
    tokenTimeout: number;
    sessionTimeout: number;
    tokenSessionTimeout: number;
    tokenActiveTimeout: number;
    loginDevice: string;
    tag: any;
  };
}

/**
 * Login
 *
 * @param email email
 * @param password Password
 */
export function fetchLogin(email: string, password: string) {
  return request<LoginResponse>({
    url: '/user/login',
    method: 'post',
    data: {
      email,
      password
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/user/info' });
}
export function fetchLogout() {
  return request<Api.Auth.LoginInfos>({ url: '/user/doLogout', method: 'get'});
}
/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}

// /** 更新密码（首次登录） */
// export function fetchUpdatePassword(newPassword: string) {
//   return request({
//     url: '/user/updatePassword',
//     method: 'post',
//     data: {
//       newPassword
//     }
//   });
// }

/** 更新用户信息 */
export function fetchUpdateUserInfo(data: {
  password: string;
  userId: number;
  name?: string;
  stuId?: string;
  cardId?: string;
  academy?: string;
  avatar?: string;
  cardPhoto?: string;
}) {
  return request({
    url: '/user/update',
    method: 'post',
    data
  });
}
