<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { useMessage } from 'naive-ui';
import { verifyUserIdentity } from '@/service/api/user';
import { uploadFile, getFileUrl } from '@/service/api/file';
import { FilePurpose } from '@/enums/filePurpose';

defineOptions({
  name: 'IdentityVerify'
});

const authStore = useAuthStore();
const message = useMessage();
const loading = ref(false);
const uploadLoading = ref(false);

const userInfo = computed(() => authStore.userInfo);

interface FormModel {
  name: string;
  stuId: string;
  cardId: string;
  academy: string;
  userType: number;
  cardPhoto: string;
}

const model = reactive<FormModel>({
  name: userInfo.value?.name || '',
  stuId: userInfo.value?.stuId || '',
  cardId: userInfo.value?.cardId || '',
  academy: userInfo.value?.academy || '',
  userType: userInfo.value?.userType || 1, // 1-学生，2-教师
  cardPhoto: userInfo.value?.cardPhoto || ''
});

const rules = computed(() => {
  return {
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    stuId: [
      { required: true, message: '请输入学号/工号', trigger: 'blur' }
    ],
    cardId: [
      { required: true, message: '请输入一卡通号', trigger: 'blur' },
      { pattern: /^\d+$/, message: '一卡通号必须是数字', trigger: 'blur' }
    ],
    academy: [
      { required: true, message: '请输入学院', trigger: 'blur' }
    ],
    cardPhoto: [
      { required: true, message: '请上传一卡通照片', trigger: 'change' }
    ]
  };
});

async function handleUploadCardPhoto(options: any) {
  uploadLoading.value = true;
  const { file, onFinish } = options;
  try {
    // 调用上传API
    const fileUrl = await uploadFile(file.file as File, FilePurpose.CARD);
    if (fileUrl) {
      model.cardPhoto = fileUrl;
      message.success('上传成功');
      onFinish();
    } else {
      message.error('上传失败：未获取到文件URL');
    }
  } catch (error: any) {
    message.error(error.message || '上传失败');
  } finally {
    uploadLoading.value = false;
  }
}

async function handleSubmit() {
  try {
    loading.value = true;
    
    const success = await authStore.verifyIdentity({
      name: model.name,
      stuId: model.stuId,
      cardId: model.cardId,
      academy: model.academy,
      userType: model.userType,
      cardPhoto: model.cardPhoto
    });
    
    if (success) {
      message.success('身份认证信息已提交，请等待审核');
    } else {
      message.error('提交失败');
    }
  } catch (error: any) {
    message.error(error.message || '提交失败');
  } finally {
    loading.value = false;
  }
}

// 获取状态类型
function getStatusType(status: number | undefined) {
  switch (status) {
    case 1: return 'success';
    case 2: return 'warning';
    case 3: return 'error';
    case 5: return 'info';
    default: return 'default';
  }
}

// 获取状态文本
function getStatusText(status: number | undefined) {
  switch (status) {
    case 1: return '已认证';
    case 2: return '审核中';
    case 3: return '已禁用';
    case 4: return '已注销';
    case 5: return '待上传证件';
    default: return '未认证';
  }
}
</script>

<template>
  <div class="identity-verify">
    <NCard title="身份认证" class="mb-4">
      <template #header-extra>
        <NTag :type="getStatusType(userInfo?.status)">
          {{ getStatusText(userInfo?.status) }}
        </NTag>
      </template>
      
      <div v-if="userInfo?.status === 1" class="verified-info">
        <NResult status="success" title="身份已认证" description="您已完成身份认证，可以使用平台的全部功能">
          <template #footer>
            <NButton type="primary" @click="$router.push('/profile')">返回个人中心</NButton>
          </template>
        </NResult>
      </div>
      
      <div v-else-if="userInfo?.status === 2" class="verifying-info">
        <NResult status="info" title="审核中" description="您的身份认证申请正在审核中，请耐心等待">
          <template #footer>
            <NButton type="primary" @click="$router.push('/profile')">返回个人中心</NButton>
          </template>
        </NResult>
      </div>
      
      <div v-else-if="userInfo?.status === 3" class="banned-info">
        <NResult status="error" title="账号已禁用" :description="'原因: ' + (userInfo?.statusReason || '违反平台规则')">
          <template #footer>
            <NButton type="primary" @click="$router.push('/profile')">返回个人中心</NButton>
          </template>
        </NResult>
      </div>
      
      <div v-else-if="userInfo?.status === 5" class="need-upload-info">
        <div class="verify-form">
          <NAlert type="warning" class="mb-4">
            <p>您需要上传一卡通证件完成身份认证，才能使用平台的全部功能</p>
            <p v-if="userInfo?.statusReason" class="mt-2 text-error-500">上一次提交未通过，原因：{{ userInfo.statusReason }}</p>
          </NAlert>
          
          <p class="mb-4 text-gray-500">
            请填写真实的身份信息，提交后需要等待管理员审核。只有通过身份认证的用户才能发布技能服务和与其他用户进行交互。
          </p>
          
          <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="auto">
            <NFormItem label="身份类型" path="userType">
              <NRadioGroup v-model:value="model.userType">
                <NRadio :value="1">学生</NRadio>
                <NRadio :value="2">教师</NRadio>
              </NRadioGroup>
            </NFormItem>
            
            <NFormItem label="姓名" path="name">
              <NInput v-model:value="model.name" placeholder="请输入姓名" />
            </NFormItem>
            
            <NFormItem :label="model.userType === 1 ? '学号' : '工号'" path="stuId">
              <NInput v-model:value="model.stuId" :placeholder="model.userType === 1 ? '请输入学号' : '请输入工号'" />
            </NFormItem>
            
            <NFormItem label="一卡通号" path="cardId">
              <NInput v-model:value="model.cardId" placeholder="请输入一卡通号" />
            </NFormItem>
            
            <NFormItem label="学院" path="academy">
              <NInput v-model:value="model.academy" placeholder="请输入学院" />
            </NFormItem>
            
            <NFormItem label="一卡通照片" path="cardPhoto">
              <NUpload
                :max="1"
                list-type="image"
                :custom-request="handleUploadCardPhoto"
                :show-file-list="true"
                accept="image/*"
              >
                <NButton :loading="uploadLoading">上传一卡通照片</NButton>
              </NUpload>
              <template #help>
                <p class="text-xs text-gray-400">请上传清晰的一卡通照片，用于身份验证</p>
              </template>
            </NFormItem>
            
            <NFormItem>
              <NButton type="primary" :loading="loading" @click="handleSubmit">提交认证</NButton>
            </NFormItem>
          </NForm>
        </div>
      </div>
      
      <div v-else class="verify-form">
        <p class="mb-4 text-gray-500">
          请填写真实的身份信息，提交后需要等待管理员审核。只有通过身份认证的用户才能发布技能服务和与其他用户进行交互。
        </p>
        
        <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="auto">
          <NFormItem label="身份类型" path="userType">
            <NRadioGroup v-model:value="model.userType">
              <NRadio :value="1">学生</NRadio>
              <NRadio :value="2">教师</NRadio>
            </NRadioGroup>
          </NFormItem>
          
          <NFormItem label="姓名" path="name">
            <NInput v-model:value="model.name" placeholder="请输入姓名" />
          </NFormItem>
          
          <NFormItem :label="model.userType === 1 ? '学号' : '工号'" path="stuId">
            <NInput v-model:value="model.stuId" :placeholder="model.userType === 1 ? '请输入学号' : '请输入工号'" />
          </NFormItem>
          
          <NFormItem label="一卡通号" path="cardId">
            <NInput v-model:value="model.cardId" placeholder="请输入一卡通号" />
          </NFormItem>
          
          <NFormItem label="学院" path="academy">
            <NInput v-model:value="model.academy" placeholder="请输入学院" />
          </NFormItem>
          
          <NFormItem label="一卡通照片" path="cardPhoto">
            <NUpload
              :max="1"
              list-type="image"
              :custom-request="handleUploadCardPhoto"
              :show-file-list="true"
              accept="image/*"
            >
              <NButton :loading="uploadLoading">上传一卡通照片</NButton>
            </NUpload>
            <template #help>
              <p class="text-xs text-gray-400">请上传清晰的一卡通照片，用于身份验证</p>
            </template>
          </NFormItem>
          
          <NFormItem>
            <NButton type="primary" :loading="loading" @click="handleSubmit">提交认证</NButton>
          </NFormItem>
        </NForm>
      </div>
    </NCard>
    
    <NCard title="认证须知" class="mb-4">
      <NThing>
        <template #header>身份认证说明</template>
        <template #description>
          <ol class="list-decimal pl-5">
            <li>请确保提供的信息真实有效，提交的一卡通照片清晰可见</li>
            <li>审核通常会在1-2个工作日内完成</li>
            <li>如果审核未通过，您可以修改信息后重新提交</li>
            <li>只有完成身份认证的用户才能使用以下功能：</li>
            <ul class="list-disc pl-5 mt-2">
              <li>发布技能服务</li>
              <li>购买技能服务</li>
              <li>发布动态</li>
              <li>评论和点赞</li>
              <li>参与交易活动</li>
            </ul>
          </ol>
        </template>
      </NThing>
    </NCard>
  </div>
</template>

<style scoped>
.identity-verify {
  max-width: 800px;
  margin: 0 auto;
}
</style> 