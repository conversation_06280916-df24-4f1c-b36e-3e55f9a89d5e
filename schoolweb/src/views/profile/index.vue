<template>
  <div class="profile-container">
    <n-card title="个人资料设置" class="max-w-800px mx-auto mt-4">
      <!-- 身份认证状态与链接 -->
      <div class="mb-4 p-4 rounded bg-gray-50 flex items-center justify-between">
        <div class="flex items-center">
          <n-icon size="24" class="mr-2 text-blue-500">
            <svg-icon icon="material-symbols:verified-user-outline" />
          </n-icon>
          <div>
            <span class="font-medium">身份认证状态：</span>
            <n-tag :type="getStatusType(authStore.userInfo.status)">
              {{ getStatusText(authStore.userInfo.status) }}
            </n-tag>
            <div v-if="authStore.userInfo.statusReason" class="text-sm text-gray-500 mt-1">
              原因：{{ authStore.userInfo.statusReason }}
            </div>
          </div>
        </div>
        <n-button 
          v-if="authStore.userInfo.status !== 1 && authStore.userInfo.status !== 2" 
          type="primary" 
          @click="goToIdentityVerify"
        >
          前往认证
        </n-button>
        <n-button 
          v-else-if="authStore.userInfo.status === 2" 
          disabled
        >
          审核中
        </n-button>
        <n-button 
          v-else
          type="success" 
          ghost
        >
          已认证
        </n-button>
      </div>
      
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <div class="flex gap-8">
          <n-form-item label="头像" path="avatar">
            <div class="avatar-upload-container">
              <n-upload
                :custom-request="customRequest"
                :max="1"
                accept="image/*"
                :show-file-list="false"
              >
                <n-avatar
                  v-if="formData.avatar"
                  :src="avatarUrl"
                  :size="100"
                  class="cursor-pointer hover:opacity-80 transition-opacity"
                />
                <div v-else class="upload-placeholder">
                  <n-icon size="24">
                    <svg-icon icon="material-symbols:cloud-upload" />
                  </n-icon>
                  <span class="upload-text">点击上传</span>
                </div>
              </n-upload>
            </div>
          </n-form-item>

          <n-form-item v-if="authStore.userInfo.status === 1" label="一卡通照片" path="cardPhoto">
            <div class="card-upload-container">
              <img
                v-if="formData.cardPhoto"
                :src="cardPhotoUrl"
                class="card-photo"
                alt="一卡通照片"
              />
              <div v-else class="upload-placeholder">
                <span class="upload-text">无照片</span>
              </div>
            </div>
          </n-form-item>
        </div>

        <n-form-item label="姓名" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入姓名" />
        </n-form-item>
        <n-form-item v-if="authStore.userInfo.status === 1" label="学号" path="stuId">
          <n-input v-model:value="formData.stuId" placeholder="请输入学号" disabled />
        </n-form-item>
        <n-form-item v-if="authStore.userInfo.status === 1" label="一卡通号" path="cardId">
          <n-input v-model:value="formData.cardId" placeholder="请输入一卡通号" disabled />
        </n-form-item>
        <n-form-item v-if="authStore.userInfo.status === 1" label="学院" path="academy">
          <n-input v-model:value="formData.academy" placeholder="请输入学院" />
        </n-form-item>
        <n-form-item label="邮箱">
          <n-input v-model:value="formData.email" disabled />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="handleSubmit">保存修改</n-button>
        </n-form-item>
      </n-form>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { useRouter } from 'vue-router';
import type { UserInfo } from '@/service/api/user';
import { updateUser } from '@/service/api/user';
import { uploadFile, getFileUrl } from '@/service/api/file';
import type { FormRules, UploadFileInfo, UploadCustomRequestOptions } from 'naive-ui';
import { FilePurpose } from '@/enums/filePurpose';

const authStore = useAuthStore();
const formRef = ref();
const router = useRouter();

interface DisplayUserInfo {
  userId: number;
  name: string;
  stuId: string;
  cardId: string;
  academy: string;
  email: string;
  avatar: string;
  cardPhoto: string;
}

const formData = reactive<DisplayUserInfo>({
  userId: Number(authStore.userInfo.userId),
  name: authStore.userInfo.name,
  stuId: authStore.userInfo.stuId,
  cardId: authStore.userInfo.cardId,
  academy: authStore.userInfo.academy || '',
  email: authStore.userInfo.email || '',
  avatar: authStore.userInfo.avatar || '',
  cardPhoto: authStore.userInfo.cardPhoto || ''
});

// 计算头像完整URL
const avatarUrl = computed(() => getFileUrl(formData.avatar));
// 计算一卡通照片完整URL
const cardPhotoUrl = computed(() => getFileUrl(formData.cardPhoto));

const rules: FormRules = {
  name: {
    required: true,
    message: '请输入姓名',
    trigger: 'blur'
  }
};

// 获取状态类型
function getStatusType(status: number) {
  switch(status) {
    case 1: return 'success';
    case 2: return 'warning';
    case 3: return 'error';
    case 5: return 'info';
    default: return 'error';
  }
}

// 获取状态文本
function getStatusText(status: number) {
  switch(status) {
    case 1: return '已认证';
    case 2: return '审核中';
    case 3: return '已禁用';
    case 4: return '已注销';
    case 5: return '待上传证件';
    default: return '未认证';
  }
}

// 跳转到身份认证页面
function goToIdentityVerify() {
  // 直接跳转到认证页面，并传递路由对象
  router.push({
    path: '/profile/identity',
    query: { redirect: router.currentRoute.value.fullPath }
  });
}

function beforeUpload() {
  return true;
}

const customRequest = async (options: UploadCustomRequestOptions) => {
  try {
    const { file } = options.file;
    const fileUrl = await uploadFile(file as File, FilePurpose.AVATAR);
    if (fileUrl) {
      // 更新本地数据
      formData.avatar = fileUrl;

      // 更新store中的用户信息
      authStore.userInfo = {
        ...authStore.userInfo,
        avatar: fileUrl
      };

      window.$message?.success('头像上传成功');
    } else {
      window.$message?.error('上传失败：未获取到文件URL');
    }
  } catch (error) {
    console.error('Upload error:', error);
    window.$message?.error('头像上传失败');
  }
};

// 一卡通照片上传
const cardPhotoRequest = async (options: UploadCustomRequestOptions) => {
  try {
    const { file } = options.file;
    const fileUrl = await uploadFile(file as File, FilePurpose.CARD);
    if (fileUrl) {
      // 更新本地数据
      formData.cardPhoto = fileUrl;

      // 更新store中的用户信息
      authStore.userInfo = {
        ...authStore.userInfo,
        cardPhoto: fileUrl
      };

      window.$message?.success('一卡通照片上传成功');
    } else {
      window.$message?.error('上传失败：未获取到文件URL');
    }
  } catch (error) {
    console.error('Upload error:', error);
    window.$message?.error('一卡通照片上传失败');
  }
};

async function handleSubmit() {
  try {
    await formRef.value?.validate();
    // 只提交允许修改的字段
    await updateUser({
      userId: formData.userId,
      name: formData.name,
      academy: formData.academy,
      avatar: formData.avatar,
      cardPhoto: formData.cardPhoto
    });
    window.$message?.success('保存成功');
    // 更新store中的用户信息，保持其他字段不变
    authStore.userInfo = {
      ...authStore.userInfo,
      name: formData.name,
      academy: formData.academy,
      avatar: formData.avatar,
      cardPhoto: formData.cardPhoto
    };
  } catch (error) {
    window.$message?.error('保存失败');
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.avatar-upload-container {
  width: 100px;
  height: 100px;
}

.card-upload-container {
  width: 164px;  /* 保持1716:1050的比例，缩放到合适大小 */
  height: 100px;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fafafa;
}

.upload-placeholder:hover {
  border-color: #18a058;
  background-color: #f0f0f0;
}

.upload-text {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

:deep(.n-upload-trigger) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

:deep(.n-avatar) {
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.card-photo {
  width: 164px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: opacity 0.3s;
}

.card-photo:hover {
  opacity: 0.8;
}
</style>
