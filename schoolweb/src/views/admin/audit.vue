<script setup lang="ts">
import { ref, reactive, onMounted, h, computed } from 'vue';
import { fetchAuditList, auditItem, fetchAuditDetail } from '@/service/api';
import { fetchUserInfo } from '@/service/api/user';
import { fetchSkillDetail } from '@/service/api/skill';
import { getFileUrl } from '@/service/api/file';
import type { AuditItem } from '@/service/api/admin';
import { useRouteStore } from '@/store/modules/route';
import { NButton, NCard, NDataTable, NDatePicker, NDescriptions, NDescriptionsItem, NEmpty, NForm, NFormItem, NInput, NModal, NRadio, NRadioGroup, NSelect, NSpin, NTag, NImage } from 'naive-ui';
import { useAuthStore } from '@/store/modules/auth';

defineOptions({
  name: 'AdminAudit'
});

// 检查权限
const authStore = useAuthStore();
// 这里用布尔值而非计算属性，避免后续引用时产生冲突
let isAdmin = false;
if (authStore.userInfo?.roles) {
  isAdmin = authStore.userInfo.roles.includes('superAdmin');
}

// 添加路由到菜单
const routeStore = useRouteStore();
// 检查菜单是否已存在审核管理选项
if (!routeStore.menus.some(menu => menu.key === 'audit-management')) {
  const auditManagementMenu = {
    key: 'audit-management',
    label: '审核管理',
    routeKey: undefined as unknown as App.Global.RouteKey, // 使用类型转换处理
    routePath: '/admin/audit' as unknown as App.Global.RoutePath, // 使用类型转换处理 
    icon: () => h('i', { class: 'i-mdi:clipboard-check text-16px' }),
  };
  
  routeStore.menus.push(auditManagementMenu);
}

// 审核列表
const auditList = ref<AuditItem[]>([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 筛选条件
const filterForm = reactive({
  type: undefined as string | undefined,
  status: undefined as number | undefined,
  keyword: '',
  startTime: null as null | number,
  endTime: null as null | number
});

// 审核操作对话框
const showAuditModal = ref(false);
const auditForm = reactive({
  id: 0,
  status: 1, // 1-通过，2-拒绝 (与数据库audits表一致)
  reason: '',
  targetName: '',
  targetType: ''
});

const typeOptions = [
  { label: '全部类型', value: undefined },
  { label: '用户认证', value: 'user' },
  { label: '技能服务', value: 'skill' }
];

const statusOptions = [
  { label: '全部状态', value: undefined },
  { label: '待审核', value: 0 }, // 与数据库audits表一致: 0-待审核
  { label: '已通过', value: 1 }, // 与数据库audits表一致: 1-通过
  { label: '已拒绝', value: 2 }  // 与数据库audits表一致: 2-拒绝
];

// 详情对话框
const showDetailModal = ref(false);
const detailLoading = ref(false);
const currentDetailItem = ref<AuditItem | null>(null);
const detailData = ref<any>(null);

// 获取审核列表
async function getAuditList(isReset = false) {
  if (isReset) {
    pagination.page = 1;
  }
  
  loading.value = true;
  try {
    const res = await fetchAuditList({
      ...filterForm,
      page: pagination.page,
      pageSize: pagination.pageSize,
      startTime: filterForm.startTime ? new Date(filterForm.startTime).toISOString() : undefined,
      endTime: filterForm.endTime ? new Date(filterForm.endTime).toISOString() : undefined
    });
    
    if (res.data?.records) {
      auditList.value = res.data.records;
      pagination.total = res.data.total;
    }
  } catch (error) {
    console.error('获取审核列表失败', error);
    window.$message?.error('获取审核列表失败，请检查您的权限');
  } finally {
    loading.value = false;
  }
}

// 打开审核对话框
function openAuditModal(item: AuditItem) {
  if (!isAdmin) {
    window.$message?.error('您没有审核权限，请联系超级管理员');
    return;
  }
  
  auditForm.id = item.auditId;
  auditForm.status = 1;
  auditForm.reason = '';
  auditForm.targetName = item.targetName;
  auditForm.targetType = item.targetType;
  showAuditModal.value = true;
}

// 提交审核
async function submitAudit() {
  if (!isAdmin) {
    window.$message?.error('您没有审核权限，请联系超级管理员');
    showAuditModal.value = false;
    return;
  }
  
  try {
    await auditItem(auditForm.id, {
      status: auditForm.status,
      reason: auditForm.status === 2 ? auditForm.reason : undefined
    });
    
    window.$message?.success('审核操作成功');
    showAuditModal.value = false;
    getAuditList();
  } catch (error) {
    console.error('审核操作失败', error);
    window.$message?.error('审核操作失败，请检查您的权限');
  }
}

// 查看详情
async function viewDetail(item: AuditItem) {
  currentDetailItem.value = item;
  detailData.value = null;
  showDetailModal.value = true;
  detailLoading.value = true;
  
  try {
    // 使用新的API获取审核详情
    const response = await fetchAuditDetail(item.auditId);
    console.log('审核详情响应:', response);
    
    // 临时解决方案：使用any类型绕过类型检查
    const anyResponse = response as any;
    if (anyResponse && anyResponse.data) {
      detailData.value = anyResponse.data.targetDetail;
      console.log('详情数据:', detailData.value);
    } else {
      window.$message?.warning('获取详情信息失败');
    }
  } catch (error) {
    console.error('获取详情失败', error);
    window.$message?.error('获取详情失败');
  } finally {
    detailLoading.value = false;
  }
}

// 获取用户状态文本
function getUserStatusText(status: number) {
  switch (status) {
    case 1: return '正常';
    case 2: return '待审核';
    case 3: return '已禁用';
    case 4: return '已注销';
    case 5: return '待上传证件';
    default: return '未知';
  }
}

// 获取用户状态标签类型
function getUserStatusType(status: number) {
  switch (status) {
    case 1: return 'success';
    case 2: return 'warning';
    case 3: return 'error';
    case 4: return 'info';
    case 5: return 'default';
    default: return 'default';
  }
}

// 获取审核状态文本
function getStatusText(status: number) {
  switch (status) {
    case 0: // 与数据库audits表一致: 0-待审核
      return '待审核';
    case 1: // 与数据库audits表一致: 1-通过
      return '已通过';
    case 2: // 与数据库audits表一致: 2-拒绝
      return '已拒绝';
    default:
      return '未知';
  }
}

// 获取状态标签类型
function getStatusType(status: number) {
  switch (status) {
    case 0: // 待审核
      return 'warning';
    case 1: // 已通过
      return 'success';
    case 2: // 已拒绝
      return 'error';
    default:
      return 'default';
  }
}

// 用户状态转变提示
const statusChangeInfo = computed(() => {
  if (auditForm.targetType !== 'user') return null;
  
  if (auditForm.status === 1) {
    return {
      before: '待审核',
      after: '已认证',
      desc: '用户将可以正常使用平台的所有功能'
    };
  } else if (auditForm.status === 2) {
    return {
      before: '待审核',
      after: '待上传证件',
      desc: '用户需要重新上传一卡通证件进行认证'
    };
  }
  
  return null;
});

// 分页变化
function handlePageChange(page: number) {
  pagination.page = page;
  getAuditList();
}

// 搜索
function handleSearch() {
  getAuditList(true);
}

// 重置筛选
function resetFilter() {
  filterForm.type = undefined;
  filterForm.status = undefined;
  filterForm.keyword = '';
  filterForm.startTime = null;
  filterForm.endTime = null;
  handleSearch();
}

onMounted(() => {
  getAuditList();
});
</script>

<template>
  <div class="p-24px">
    <NCard title="审核管理" :bordered="false" class="shadow-sm mb-16px">
      <!-- 筛选条件 -->
      <NForm inline :label-width="80" :model="filterForm">
        <NFormItem label="类型">
          <NSelect v-model:value="filterForm.type" :options="typeOptions" style="width: 160px;" />
        </NFormItem>
        
        <NFormItem label="状态">
          <NSelect v-model:value="filterForm.status" :options="statusOptions" style="width: 160px;" />
        </NFormItem>
        
        <NFormItem label="关键词">
          <NInput v-model:value="filterForm.keyword" placeholder="输入名称或内容关键词" />
        </NFormItem>
        
        <NFormItem label="提交时间">
          <NDatePicker
            v-model:value="filterForm.startTime"
            type="datetime"
            placeholder="开始时间"
            style="width: 180px;"
            clearable
          />
          <span class="mx-8px">至</span>
          <NDatePicker
            v-model:value="filterForm.endTime"
            type="datetime"
            placeholder="结束时间"
            style="width: 180px;"
            clearable
          />
        </NFormItem>
        
        <NFormItem>
          <NButton type="primary" @click="handleSearch">搜索</NButton>
          <NButton class="ml-12px" @click="resetFilter">重置</NButton>
        </NFormItem>
      </NForm>
    </NCard>
    
    <!-- 权限提示 -->
    <div v-if="!isAdmin" class="mb-16px p-16px bg-warning-100 text-warning-700 rounded">
      <div class="font-bold">权限提示</div>
      <p>您当前没有审核权限，只能查看审核记录，无法进行审核操作。请联系超级管理员获取权限。</p>
      <p>当前角色: {{ authStore.userInfo?.roles?.join(', ') || '无' }}</p>
    </div>
    
    <!-- 审核列表 -->
    <NCard :bordered="false" class="shadow-sm">
      <NSpin :show="loading">
        <NDataTable
          :columns="[
            {
              title: '序号',
              key: 'index',
              width: 80,
              render: (row, index) => index + 1 + (pagination.page - 1) * pagination.pageSize
            },
            { title: '类型', key: 'type', width: 100, render: (row) => row.targetType === 'user' ? '用户认证' : '技能服务' },
            { title: '名称', key: 'targetName', width: 180 },
            { 
              title: '状态', 
              key: 'status', 
              width: 100,
              render: (row) => h(NTag as any, { type: getStatusType(row.status) }, { default: () => getStatusText(row.status) })
            },
            { 
              title: '内容摘要', 
              key: 'content', 
              ellipsis: { tooltip: true }
            },
            { title: '提交时间', key: 'submitTime', width: 180 },
            { 
              title: '审核信息', 
              key: 'auditInfo',
              width: 200,
              render: (row) => {
                if (row.status === 0) return '待审核';
                return [
                  h('div', `审核人: ${row.auditorName || '-'}`),
                  h('div', `时间: ${row.auditTime || '-'}`),
                  row.reason ? h('div', { class: 'text-error' }, `原因: ${row.reason}`) : null
                ];
              }
            },
            {
              title: '操作',
              key: 'actions',
              width: 180,
              render: (row) => {
                const buttons = [
                  h(
                    NButton as any,
                    {
                      size: 'small',
                      onClick: () => viewDetail(row)
                    },
                    { default: () => '查看详情' }
                  )
                ];
                
                // 只有待审核的项目显示审核按钮，并且需要有权限
                if (row.status === 0 && isAdmin) {
                  buttons.push(
                    h(
                      NButton as any,
                      {
                        type: 'primary',
                        size: 'small',
                        class: 'ml-8px',
                        onClick: () => openAuditModal(row)
                      },
                      { default: () => '审核' }
                    )
                  );
                }
                
                return buttons;
              }
            }
          ]"
          :data="auditList"
          :pagination="{
            page: pagination.page,
            pageSize: pagination.pageSize,
            pageCount: Math.ceil(pagination.total / pagination.pageSize),
            showSizePicker: true,
            pageSizes: [10, 20, 30, 50],
            onChange: handlePageChange,
            onUpdatePageSize: (pageSize) => {
              pagination.pageSize = pageSize;
              pagination.page = 1;
              getAuditList();
            }
          }"
          :bordered="false"
        />
        
        <NEmpty v-if="auditList.length === 0 && !loading" description="暂无数据" />
      </NSpin>
    </NCard>
    
    <!-- 审核对话框 -->
    <NModal v-model:show="showAuditModal" preset="card" title="审核操作" style="width: 500px;">
      <NForm :model="auditForm" label-placement="left" :label-width="80">
        <NFormItem label="名称">
          <span>{{ auditForm.targetName }}</span>
        </NFormItem>
        
        <NFormItem label="类型">
          <span>{{ auditForm.targetType === 'user' ? '用户认证' : '技能服务' }}</span>
        </NFormItem>
        
        <NFormItem label="审核结果">
          <NRadioGroup v-model:value="auditForm.status">
            <NRadio :value="1">通过</NRadio>
            <NRadio :value="2">拒绝</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem v-if="auditForm.status === 2" label="拒绝原因">
          <NInput v-model:value="auditForm.reason" type="textarea" :rows="3" placeholder="请输入拒绝原因" />
        </NFormItem>
        
        <div v-if="statusChangeInfo" class="bg-info-50 p-12px rounded mb-16px">
          <div class="text-info-600 font-medium mb-4px">状态变更提示</div>
          <div class="text-sm">
            <div>用户状态将从 <NTag size="small" type="warning">{{ statusChangeInfo.before }}</NTag> 变更为 
              <NTag size="small" :type="auditForm.status === 1 ? 'success' : 'info'">{{ statusChangeInfo.after }}</NTag>
            </div>
            <div class="mt-4px">{{ statusChangeInfo.desc }}</div>
          </div>
        </div>
        
        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showAuditModal = false">取消</NButton>
          <NButton type="primary" :disabled="auditForm.status === 2 && !auditForm.reason" @click="submitAudit">
            确认
          </NButton>
        </div>
      </NForm>
    </NModal>

    <!-- 详情对话框 -->
    <NModal v-model:show="showDetailModal" preset="card" :title="currentDetailItem?.targetType === 'user' ? '用户详情' : '技能服务详情'" style="width: 650px;">
      <NSpin :show="detailLoading">
        <div v-if="!detailData && !detailLoading" class="empty-detail">
          <NEmpty description="未找到详情数据" />
        </div>
        
        <!-- 用户详情 -->
        <div v-else-if="currentDetailItem?.targetType === 'user' && detailData" class="user-detail">
          <div class="flex items-center mb-16px">
            <div class="mr-16px">
              <NImage
                v-if="detailData.avatar"
                :src="getFileUrl(detailData.avatar)"
                width="80"
                height="80"
                object-fit="cover"
                :preview-disabled="true"
                class="rounded-full"
              />
              <div v-else class="avatar-placeholder">
                {{ detailData.name?.[0] || '?' }}
              </div>
            </div>
            <div>
              <h3 class="text-lg font-medium">{{ detailData.name }}</h3>
              <div class="mt-2">
                <NTag :type="getUserStatusType(detailData.status)">
                  {{ getUserStatusText(detailData.status) }}
                </NTag>
                <span class="ml-2 text-gray-500">{{ detailData.roles?.join(', ') || '无角色' }}</span>
              </div>
            </div>
          </div>
          
          <NDescriptions label-placement="left" :column="1" bordered>
            <NDescriptionsItem label="用户ID">{{ detailData.userId }}</NDescriptionsItem>
            <NDescriptionsItem label="姓名">{{ detailData.name }}</NDescriptionsItem>
            <NDescriptionsItem label="学号/工号">{{ detailData.stuId || '-' }}</NDescriptionsItem>
            <NDescriptionsItem label="学院">{{ detailData.academy || '-' }}</NDescriptionsItem>
            <NDescriptionsItem label="一卡通号">{{ detailData.cardId || '-' }}</NDescriptionsItem>
            <NDescriptionsItem label="邮箱">{{ detailData.email }}</NDescriptionsItem>
            <NDescriptionsItem label="用户类型">{{ detailData.userType === 1 ? '学生' : '教师' }}</NDescriptionsItem>
            <NDescriptionsItem label="审核内容">{{ currentDetailItem?.content }}</NDescriptionsItem>
            <NDescriptionsItem label="一卡通照片">
              <div>
                <NImage
                  v-if="detailData.cardPhoto"
                  :src="getFileUrl(detailData.cardPhoto)"
                  width="200"
                  height="120"
                  object-fit="cover"
                  :preview-disabled="false"
                />
                <div v-else class="text-gray-400">未上传一卡通照片</div>
              </div>
            </NDescriptionsItem>
          </NDescriptions>
        </div>
        
        <!-- 技能服务详情 -->
        <div v-else-if="currentDetailItem?.targetType === 'skill' && detailData" class="skill-detail">
          <h3 class="text-lg font-medium mb-16px">{{ detailData.title }}</h3>
          
          <NDescriptions label-placement="left" :column="1" bordered>
            <NDescriptionsItem label="服务ID">{{ detailData.serviceId }}</NDescriptionsItem>
            <NDescriptionsItem label="类型">{{ detailData.categoryName }}</NDescriptionsItem>
            <NDescriptionsItem label="发布者">{{ detailData.userName }}</NDescriptionsItem>
            <NDescriptionsItem label="价格">{{ detailData.price }} 学币</NDescriptionsItem>
            <NDescriptionsItem label="服务方式">{{ detailData.serviceType === 1 ? '线上' : '线下' }}</NDescriptionsItem>
            <NDescriptionsItem label="服务时长">{{ detailData.duration }} 分钟</NDescriptionsItem>
            <NDescriptionsItem label="服务描述">{{ detailData.description }}</NDescriptionsItem>
            <NDescriptionsItem label="审核内容">{{ currentDetailItem?.content }}</NDescriptionsItem>
            <NDescriptionsItem label="服务图片" v-if="detailData.images && detailData.images.length > 0">
              <div class="flex flex-wrap gap-8px">
                <NImage
                  v-for="(image, index) in detailData.images"
                  :key="index"
                  :src="getFileUrl(image)"
                  width="100"
                  height="100"
                  object-fit="cover"
                  :preview-disabled="false"
                />
              </div>
            </NDescriptionsItem>
          </NDescriptions>
        </div>
      </NSpin>
      
      <template #footer>
        <div class="flex justify-end">
          <NButton @click="showDetailModal = false">关闭</NButton>
          <!-- 如果是待审核状态且有权限，显示审核按钮 -->
          <NButton 
            v-if="currentDetailItem && currentDetailItem.status === 0 && isAdmin"
            type="primary"
            class="ml-12px"
            @click="() => {
              if (currentDetailItem) {
                showDetailModal = false;
                openAuditModal(currentDetailItem);
              }
            }"
          >
            审核
          </NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32px;
  color: #666;
}
</style>