<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getUserVerificationStatus } from '@/service/api/user';
import { useAuthStore } from '@/store/modules/auth';

defineOptions({
  name: 'UserProfile'
});

const appStore = useAppStore();
const themeStore = useThemeStore();
const { formRef, validate } = useNaiveForm();
const avatarUrl = ref('/src/assets/img/default-avatar.jpg');
const uploading = ref(false);

// 用户认证状态相关
const verificationStatus = ref(0);
const verificationText = ref('未认证');
const verificationClass = ref('text-error bg-error-100');

interface UserProfileForm {
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  major: string;
  bio: string;
  skills: string[];
}

const formData = reactive<UserProfileForm>({
  username: 'user123',
  realName: '张三',
  email: '<EMAIL>',
  phone: '13800138000',
  department: '计算机学院',
  major: '软件工程',
  bio: '对编程和设计充满热情的学生',
  skills: ['编程', '设计']
});

const skillOptions = [
  { label: '编程', value: '编程' },
  { label: '设计', value: '设计' },
  { label: '乐器演奏', value: '乐器演奏' },
  { label: '语言翻译', value: '语言翻译' },
  { label: '摄影', value: '摄影' },
  { label: '绘画', value: '绘画' },
  { label: '视频制作', value: '视频制作' },
  { label: '写作', value: '写作' }
];

// 加载用户认证状态
async function getVerificationStatus() {
  try {
    const response = await getUserVerificationStatus();
    if (response.data && response.data.code === 200 && response.data.data) {
      verificationStatus.value = response.data.data.status;
      verificationText.value = response.data.data.statusText || '未知状态';
      
      // 设置样式
      if (verificationStatus.value === 1) {
        // 已认证
        verificationClass.value = 'text-success bg-success-100';
      } else if (verificationStatus.value === 2) {
        // 待审核
        verificationClass.value = 'text-warning bg-warning-100';
      } else {
        // 未认证或其他状态
        verificationClass.value = 'text-error bg-error-100';
      }
    } else {
      console.error('获取认证状态失败', response);
      verificationText.value = '未认证';
      verificationClass.value = 'text-error bg-error-100';
    }
  } catch (error) {
    console.error('获取认证状态出错', error);
    verificationText.value = '未认证';
    verificationClass.value = 'text-error bg-error-100';
  }
}

function handleAvatarUpload() {
  uploading.value = true;
  setTimeout(() => {
    uploading.value = false;
    window.$message?.success('头像上传成功');
  }, 1000);
}

async function handleSubmit() {
  await validate();
  window.$message?.success('个人资料保存成功');
}

onMounted(() => {
  // 页面加载时获取用户认证状态
  getVerificationStatus();
});
</script>

<template>
  <div class="p-24px">
    <NCard title="个人资料" :bordered="false" size="large" class="rounded-16px shadow-sm">
      <NSpace vertical size="large">
        <div class="flex items-center">
          <div class="mr-24px">
            <NAvatar
              :size="64"
              :src="avatarUrl"
              :style="{ backgroundColor: themeStore.themeColor }"
              fallback-src="/src/assets/img/default-avatar.jpg"
            />
          </div>
          <div>
            <NUpload
              action="#"
              :max="1"
              :show-file-list="false"
              list-type="image-card"
              accept="image/*"
              :custom-request="handleAvatarUpload"
            >
              <NButton :loading="uploading" type="primary">更换头像</NButton>
            </NUpload>
            <div class="mt-8px text-gray-400 text-12px">支持jpg、png格式，文件小于2MB</div>
          </div>
        </div>

        <!-- 身份认证状态显示 -->
        <div class="flex items-center">
          <div class="mr-16px">
            <NIcon size="24">
              <div class="i-mdi:shield-account"></div>
            </NIcon>
          </div>
          <div>
            <div class="flex items-center">
              <span class="mr-8px">身份认证状态:</span>
              <span :class="['px-8px py-2px rounded', verificationClass]">{{ verificationText }}</span>
            </div>
            <div v-if="verificationStatus !== 1" class="mt-8px">
              <NButton type="primary" size="small" tag="a" href="#/identity-verify">
                {{ verificationStatus === 2 ? '查看审核进度' : '前往认证' }}
              </NButton>
            </div>
          </div>
        </div>

        <NForm
          ref="formRef"
          :model="formData"
          label-placement="left"
          label-width="80"
          require-mark-placement="right-hanging"
          size="large"
        >
          <NGrid :cols="24" :x-gap="24">
            <NGi :span="12">
              <NFormItem label="用户名" path="username" :rule="[{ required: true, message: '请输入用户名' }]">
                <NInput v-model:value="formData.username" placeholder="请输入用户名" />
              </NFormItem>
            </NGi>
            <NGi :span="12">
              <NFormItem label="真实姓名" path="realName" :rule="[{ required: true, message: '请输入真实姓名' }]">
                <NInput v-model:value="formData.realName" placeholder="请输入真实姓名" />
              </NFormItem>
            </NGi>
            <NGi :span="12">
              <NFormItem label="邮箱" path="email" :rule="[{ required: true, message: '请输入邮箱' }]">
                <NInput v-model:value="formData.email" placeholder="请输入邮箱" />
              </NFormItem>
            </NGi>
            <NGi :span="12">
              <NFormItem label="手机号" path="phone" :rule="[{ required: true, message: '请输入手机号' }]">
                <NInput v-model:value="formData.phone" placeholder="请输入手机号" />
              </NFormItem>
            </NGi>
            <NGi :span="12">
              <NFormItem label="学院" path="department">
                <NInput v-model:value="formData.department" placeholder="请输入所在学院" />
              </NFormItem>
            </NGi>
            <NGi :span="12">
              <NFormItem label="专业" path="major">
                <NInput v-model:value="formData.major" placeholder="请输入所在专业" />
              </NFormItem>
            </NGi>
            <NGi :span="24">
              <NFormItem label="个人简介" path="bio">
                <NInput
                  v-model:value="formData.bio"
                  type="textarea"
                  placeholder="请输入个人简介"
                  :autosize="{ minRows: 3, maxRows: 5 }"
                />
              </NFormItem>
            </NGi>
            <NGi :span="24">
              <NFormItem label="技能标签" path="skills">
                <NSelect
                  v-model:value="formData.skills"
                  placeholder="请选择技能标签"
                  multiple
                  :options="skillOptions"
                  filterable
                  tag
                />
              </NFormItem>
            </NGi>
          </NGrid>
          <div class="flex justify-center mt-24px">
            <NButton type="primary" size="large" :block="false" style="min-width: 120px;" @click="handleSubmit">
              保存
            </NButton>
          </div>
        </NForm>
      </NSpace>
    </NCard>
  </div>
</template>

<style scoped></style> 