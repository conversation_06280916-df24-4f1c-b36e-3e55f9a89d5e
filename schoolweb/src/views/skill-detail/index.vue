<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useThemeStore } from '@/store/modules/theme';
import { useNaiveForm } from '@/hooks/common/form';

defineOptions({
  name: 'SkillDetail'
});

const route = useRoute();
const router = useRouter();
const themeStore = useThemeStore();
const { formRef, validate } = useNaiveForm();

const skillId = computed(() => Number(route.params.id));
const showOrderModal = ref(false);
const orderLoading = ref(false);

// 模拟从API获取技能详情数据
const skillDetail = reactive({
  id: 1,
  title: 'Web前端开发指导',
  description: '提供HTML、CSS、JavaScript等前端技术的一对一辅导，帮助你掌握前端开发技能。',
  price: 50,
  unit: '小时',
  category: 'programming',
  tags: ['HTML', 'CSS', 'JavaScript'],
  rating: 4.8,
  reviewCount: 24,
  provider: {
    id: 101,
    name: '李明',
    avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
    department: '计算机科学学院',
    creditScore: 95,
    skills: ['前端开发', 'UI设计', 'Vue', 'React'],
    completedOrders: 36,
    successRate: 98
  },
  content: `<h3>服务内容</h3>
<p>本服务提供Web前端开发一对一指导，包括但不限于以下内容：</p>
<ul>
  <li>HTML5基础与进阶</li>
  <li>CSS3样式与布局</li>
  <li>JavaScript基础与ES6+特性</li>
  <li>Vue/React框架入门</li>
  <li>前端工程化与构建工具</li>
  <li>项目实战指导</li>
</ul>

<h3>服务方式</h3>
<p>线上：通过腾讯会议或微信视频进行远程指导</p>
<p>线下：可在校内约定地点面对面指导（理工校区、文理校区）</p>

<h3>服务时间</h3>
<p>工作日：晚上18:00-22:00</p>
<p>周末及节假日：9:00-22:00</p>

<h3>服务保障</h3>
<p>1. 如对服务不满意，可在服务开始30分钟内提出终止，仅收取已进行时间的费用</p>
<p>2. 每次服务后会提供学习资料和练习题</p>
<p>3. 一周内提供免费答疑（通过微信文字形式）</p>`,
  faq: [
    {
      question: '我是零基础可以学习吗？',
      answer: '完全可以，我会根据你的基础程度定制学习计划，从最基础的概念开始讲解。'
    },
    {
      question: '一次服务多长时间？',
      answer: '标准服务时长为2小时/次，可根据需求延长或缩短。'
    },
    {
      question: '可以帮忙完成课程作业吗？',
      answer: '我可以提供思路指导和解题方法，但不会直接帮你完成作业，这样对你的学习没有帮助。'
    }
  ],
  reviews: [
    {
      id: 1,
      user: { name: '张同学', avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg' },
      rating: 5,
      content: '讲解非常清晰，针对我的问题给出了很好的解决方案，还提供了很多额外的学习资源。',
      date: '2023-04-15'
    },
    {
      id: 2,
      user: { name: '王同学', avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg' },
      rating: 4,
      content: '服务态度很好，讲解也很细致，就是有些概念讲得太深入了，对初学者有点难理解。',
      date: '2023-04-10'
    },
    {
      id: 3,
      user: { name: '李同学', avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg' },
      rating: 5,
      content: '非常专业，解决了我项目中遇到的所有问题，还教了我很多代码优化的技巧。',
      date: '2023-04-05'
    }
  ]
});

// 订单信息
const orderForm = reactive({
  serviceTime: '',
  serviceHours: 2,
  contactMethod: 'online',
  contactInfo: '',
  requirements: '',
  totalPrice: computed(() => skillDetail.price * orderForm.serviceHours)
});

// 模拟数据加载
onMounted(() => {
  // 加载技能详情数据
  console.log('加载技能ID:', skillId.value);
  // 实际项目中应该从API获取数据
});

// 下单相关方法
function openOrderModal() {
  showOrderModal.value = true;
}

async function submitOrder() {
  try {
    await validate();
    orderLoading.value = true;
    
    // 模拟API请求
    setTimeout(() => {
      orderLoading.value = false;
      showOrderModal.value = false;
      window.$message?.success('预约服务成功！');
      router.push('/order');
    }, 1000);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 联系服务提供者
function contactProvider() {
  window.$message?.info('已向服务提供者发送联系请求');
}

// 收藏服务
function toggleFavorite() {
  window.$message?.success('收藏成功');
}

// 格式化技能分类名称
function getCategoryName(category: string) {
  const categoryMap: Record<string, string> = {
    programming: '编程开发',
    design: '设计创意',
    translation: '语言翻译',
    music: '音乐才艺',
    tutoring: '学业辅导',
    photography: '摄影摄像',
    writing: '写作编辑',
    others: '其他技能'
  };
  
  return categoryMap[category] || '其他技能';
}
</script>

<template>
  <div class="p-24px">
    <div class="flex items-center mb-16px">
      <NButton quaternary circle @click="router.back()">
        <div class="i-material-symbols:arrow-back text-18px"></div>
      </NButton>
      <span class="ml-8px">返回技能列表</span>
    </div>
    
    <NGrid :cols="24" :x-gap="24" :y-gap="24" responsive="screen" item-responsive>
      <!-- 技能详情部分 -->
      <NGi span="24 s:24 m:16">
        <NCard :bordered="false" size="large" class="rounded-16px shadow-sm">
          <div class="relative">
            <div class="h-240px bg-gray-200 rounded-8px overflow-hidden">
              <img
                :src="`https://picsum.photos/800/400?random=${skillDetail.id}`"
                class="w-full h-full object-cover"
                alt="技能封面"
              />
            </div>
            
            <div class="mt-16px">
              <div class="flex items-start justify-between">
                <div>
                  <div class="flex-y-center">
                    <NTag :bordered="false" round size="small" :type="skillDetail.category === 'programming' ? 'success' : skillDetail.category === 'design' ? 'info' : skillDetail.category === 'translation' ? 'warning' : 'default'">
                      {{ getCategoryName(skillDetail.category) }}
                    </NTag>
                    <span class="ml-8px flex-y-center">
                      <div class="i-material-symbols:star text-yellow-500"></div>
                      <span class="ml-4px">{{ skillDetail.rating }}</span>
                      <span class="ml-4px text-12px text-gray-400">({{ skillDetail.reviewCount }})</span>
                    </span>
                  </div>
                  <h1 class="text-24px font-bold mt-8px">{{ skillDetail.title }}</h1>
                </div>
                <div class="flex">
                  <NButton circle @click="toggleFavorite">
                    <template #icon>
                      <div class="i-material-symbols:favorite-outline"></div>
                    </template>
                  </NButton>
                  <NButton class="ml-8px" circle @click="contactProvider">
                    <template #icon>
                      <div class="i-material-symbols:chat-outline"></div>
                    </template>
                  </NButton>
                </div>
              </div>
              
              <div class="flex-wrap mt-8px">
                <NSpace>
                  <NTag
                    v-for="tag in skillDetail.tags"
                    :key="tag"
                    size="small"
                    :bordered="false"
                    :color="{ color: '#e9f5fe', textColor: '#2080f0' }"
                  >
                    {{ tag }}
                  </NTag>
                </NSpace>
              </div>
              
              <div class="mt-16px pb-16px border-b border-gray-200">
                <div class="text-gray-700">{{ skillDetail.description }}</div>
                <div class="mt-16px text-24px font-bold text-primary">
                  {{ skillDetail.price }} <span class="text-14px text-gray-500 font-normal">学币/{{ skillDetail.unit }}</span>
                </div>
              </div>
              
              <div class="mt-16px">
                <div v-html="skillDetail.content" class="skill-content"></div>
              </div>
              
              <!-- FAQ部分 -->
              <div class="mt-24px" v-if="skillDetail.faq && skillDetail.faq.length > 0">
                <h2 class="text-18px font-bold mb-16px">常见问题</h2>
                <NCollapse>
                  <NCollapseItem
                    v-for="(item, index) in skillDetail.faq"
                    :key="index"
                    :title="item.question"
                    :name="index"
                  >
                    {{ item.answer }}
                  </NCollapseItem>
                </NCollapse>
              </div>
            </div>
          </div>
        </NCard>
        
        <!-- 评价部分 -->
        <NCard :bordered="false" size="large" class="rounded-16px shadow-sm mt-24px">
          <div>
            <h2 class="text-18px font-bold mb-16px">用户评价</h2>
            <div v-if="skillDetail.reviews && skillDetail.reviews.length > 0">
              <NList>
                <NListItem v-for="review in skillDetail.reviews" :key="review.id">
                  <div class="flex">
                    <NAvatar :src="review.user.avatar" round size="medium" />
                    <div class="ml-16px w-full">
                      <div class="flex justify-between items-center">
                        <span class="font-medium">{{ review.user.name }}</span>
                        <span class="text-gray-400 text-14px">{{ review.date }}</span>
                      </div>
                      <div class="flex mt-4px text-yellow-500">
                        <div
                          v-for="i in 5"
                          :key="i"
                          :class="i <= review.rating ? 'i-material-symbols:star' : 'i-material-symbols:star-outline'"
                        ></div>
                      </div>
                      <div class="mt-8px text-gray-700">{{ review.content }}</div>
                    </div>
                  </div>
                </NListItem>
              </NList>
            </div>
            <NEmpty v-else description="暂无评价" />
          </div>
        </NCard>
      </NGi>
      
      <!-- 侧边栏 -->
      <NGi span="24 s:24 m:8">
        <!-- 服务提供者信息 -->
        <NCard :bordered="false" size="large" class="rounded-16px shadow-sm">
          <div>
            <h2 class="text-18px font-bold mb-16px">服务提供者</h2>
            <div class="flex items-center">
              <NAvatar
                :src="skillDetail.provider.avatar"
                round
                size="large"
                :style="{ backgroundColor: themeStore.themeColor }"
              />
              <div class="ml-16px">
                <div class="font-medium">{{ skillDetail.provider.name }}</div>
                <div class="text-14px text-gray-500">{{ skillDetail.provider.department }}</div>
              </div>
            </div>
            
            <div class="mt-16px grid grid-cols-2 gap-16px">
              <div class="text-center">
                <div class="text-20px font-bold text-primary">{{ skillDetail.provider.completedOrders }}</div>
                <div class="text-14px text-gray-500">已完成订单</div>
              </div>
              <div class="text-center">
                <div class="text-20px font-bold text-primary">{{ skillDetail.provider.successRate }}%</div>
                <div class="text-14px text-gray-500">完成率</div>
              </div>
            </div>
            
            <div class="mt-16px">
              <div class="flex-y-center">
                <div class="i-material-symbols:verified text-primary"></div>
                <span class="ml-8px text-14px">信用评级：</span>
                <span class="text-16px font-medium" :style="{ color: skillDetail.provider.creditScore >= 90 ? '#18a058' : '#2080f0' }">
                  {{ skillDetail.provider.creditScore >= 90 ? 'A级' : 'B级' }}
                </span>
              </div>
              
              <div class="mt-8px">
                <div class="text-14px mb-8px">擅长技能：</div>
                <div class="flex flex-wrap gap-8px">
                  <NTag
                    v-for="skill in skillDetail.provider.skills"
                    :key="skill"
                    size="small"
                    :bordered="false"
                    :color="{ color: '#f3f3f3', textColor: '#666' }"
                  >
                    {{ skill }}
                  </NTag>
                </div>
              </div>
            </div>
            
            <div class="mt-24px">
              <NButton type="primary" block @click="openOrderModal">预约服务</NButton>
              <NButton class="mt-16px" secondary block @click="contactProvider">联系Ta</NButton>
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>
    
    <!-- 下单弹窗 -->
    <NModal
      v-model:show="showOrderModal"
      preset="card"
      title="预约服务"
      style="width: 600px"
      :bordered="false"
      :segmented="true"
      size="huge"
      :mask-closable="false"
    >
      <NForm
        ref="formRef"
        :model="orderForm"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="服务时间" path="serviceTime" :rule="{ required: true, message: '请选择服务时间' }">
          <NDatePicker
            v-model:value="orderForm.serviceTime"
            type="datetime"
            clearable
            style="width: 100%"
            :is-date-disabled="(ts: number) => ts < Date.now()"
          />
        </NFormItem>
        
        <NFormItem label="服务时长" path="serviceHours" :rule="{ required: true, message: '请选择服务时长' }">
          <NInputNumber
            v-model:value="orderForm.serviceHours"
            :min="1"
            :max="10"
            style="width: 100%"
          >
            <template #suffix>小时</template>
          </NInputNumber>
        </NFormItem>
        
        <NFormItem label="联系方式" path="contactMethod" :rule="{ required: true, message: '请选择联系方式' }">
          <NRadioGroup v-model:value="orderForm.contactMethod">
            <NRadio value="online">线上（腾讯会议/微信视频）</NRadio>
            <NRadio value="offline">线下面对面</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem label="联系信息" path="contactInfo" :rule="{ required: true, message: '请填写联系信息' }">
          <NInput
            v-model:value="orderForm.contactInfo"
            placeholder="线上请填写微信号或QQ号，线下请填写校区与约定地点"
          />
        </NFormItem>
        
        <NFormItem label="具体需求" path="requirements">
          <NInput
            v-model:value="orderForm.requirements"
            type="textarea"
            placeholder="请详细描述你的具体需求或问题，以便服务者更好地准备"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </NFormItem>
        
        <div class="py-16px flex-y-center justify-between border-t border-gray-200 mt-16px">
          <div>
            <span class="text-14px">服务费用：</span>
            <span class="text-20px font-bold text-primary">{{ orderForm.totalPrice }}</span>
            <span class="text-14px text-gray-500"> 学币</span>
          </div>
          <div>
            <NButton class="mr-16px" @click="showOrderModal = false">取消</NButton>
            <NButton type="primary" :loading="orderLoading" @click="submitOrder">确认预约</NButton>
          </div>
        </div>
      </NForm>
    </NModal>
  </div>
</template>

<style scoped>
.skill-content :deep(h3) {
  font-size: 18px;
  font-weight: bold;
  margin-top: 16px;
  margin-bottom: 8px;
}

.skill-content :deep(p) {
  margin-bottom: 8px;
}

.skill-content :deep(ul) {
  padding-left: 20px;
  margin-bottom: 16px;
}

.skill-content :deep(li) {
  margin-bottom: 4px;
}
</style> 