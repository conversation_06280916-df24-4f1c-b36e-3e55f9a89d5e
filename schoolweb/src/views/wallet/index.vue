<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { fetchWalletInfo, fetchTransactions, rechargeWallet, withdrawWallet, transferToProvider, fetchUserCredits, exchangeCredits } from '@/service/api';
import type { Wallet, Transaction } from '@/service/api/wallet';
import { useUserStore } from '@/store/modules/user';

defineOptions({
  name: 'Wallet'
});

const userStore = useUserStore();

// 钱包信息
const wallet = ref<Wallet | null>(null);
const credits = ref<number>(0);
const loading = ref(false);

// 交易记录
const transactions = ref<Transaction[]>([]);
const transactionLoading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 筛选条件
const filterForm = reactive({
  type: undefined as number | undefined,
  status: undefined as number | undefined,
  startTime: null as string | null,
  endTime: null as string | null
});

// 弹窗相关
const showRechargeModal = ref(false);
const showWithdrawModal = ref(false);
const showTransferModal = ref(false);
const showExchangeModal = ref(false);

// 表单数据
const rechargeForm = reactive({
  amount: 0
});
const withdrawForm = reactive({
  amount: 0,
  accountInfo: ''
});
const transferForm = reactive({
  targetUserId: undefined as number | undefined,
  targetUserName: '',
  amount: 0,
  remark: ''
});
const exchangeForm = reactive({
  credits: 0,
  amount: 0
});

// 计算属性
const availableBalance = computed(() => {
  if (!wallet.value) return 0;
  return wallet.value.balance - wallet.value.frozenAmount;
});

// 获取钱包信息
async function getWalletInfo() {
  loading.value = true;
  try {
    const [walletRes, creditsRes] = await Promise.all([
      fetchWalletInfo(),
      fetchUserCredits()
    ]);
    wallet.value = walletRes;
    credits.value = creditsRes.credits;
  } catch (error) {
    console.error('获取钱包信息失败', error);
  } finally {
    loading.value = false;
  }
}

// 获取交易记录
async function getTransactions(isReset = false) {
  if (isReset) {
    pagination.page = 1;
  }
  
  transactionLoading.value = true;
  try {
    const res = await fetchTransactions({
      ...filterForm,
      page: pagination.page,
      pageSize: pagination.pageSize
    });
    
    if (res.data?.records) {
      transactions.value = res.data.records;
      pagination.total = res.data.total;
    }
  } catch (error) {
    console.error('获取交易记录失败', error);
  } finally {
    transactionLoading.value = false;
  }
}

// 充值
async function handleRecharge() {
  if (rechargeForm.amount <= 0) {
    window.$message?.error('充值金额必须大于0');
    return;
  }
  
  try {
    await rechargeWallet(rechargeForm.amount);
    window.$message?.success('充值成功');
    showRechargeModal.value = false;
    rechargeForm.amount = 0;
    getWalletInfo();
    getTransactions(true);
  } catch (error) {
    console.error('充值失败', error);
  }
}

// 提现
async function handleWithdraw() {
  if (withdrawForm.amount <= 0) {
    window.$message?.error('提现金额必须大于0');
    return;
  }
  
  if (withdrawForm.amount > availableBalance.value) {
    window.$message?.error('可用余额不足');
    return;
  }
  
  if (!withdrawForm.accountInfo) {
    window.$message?.error('请填写收款账户信息');
    return;
  }
  
  try {
    await withdrawWallet(withdrawForm.amount, withdrawForm.accountInfo);
    window.$message?.success('提现申请已提交，等待处理');
    showWithdrawModal.value = false;
    withdrawForm.amount = 0;
    withdrawForm.accountInfo = '';
    getWalletInfo();
    getTransactions(true);
  } catch (error) {
    console.error('提现失败', error);
  }
}

// 转账
async function handleTransfer() {
  if (!transferForm.targetUserId) {
    window.$message?.error('请选择转账对象');
    return;
  }
  
  if (transferForm.amount <= 0) {
    window.$message?.error('转账金额必须大于0');
    return;
  }
  
  if (transferForm.amount > availableBalance.value) {
    window.$message?.error('可用余额不足');
    return;
  }
  
  try {
    await transferToProvider(transferForm.targetUserId, transferForm.amount, 0);
    window.$message?.success('转账成功');
    showTransferModal.value = false;
    transferForm.targetUserId = undefined;
    transferForm.targetUserName = '';
    transferForm.amount = 0;
    transferForm.remark = '';
    getWalletInfo();
    getTransactions(true);
  } catch (error) {
    console.error('转账失败', error);
  }
}

// 计算兑换后的虚拟币金额
function calculateExchangeAmount() {
  // 示例：1积分 = 0.1虚拟币
  exchangeForm.amount = exchangeForm.credits * 0.1;
}

// 积分兑换虚拟币
async function handleExchange() {
  if (exchangeForm.credits <= 0) {
    window.$message?.error('兑换积分必须大于0');
    return;
  }
  
  if (exchangeForm.credits > credits.value) {
    window.$message?.error('积分不足');
    return;
  }
  
  try {
    const res = await exchangeCredits(exchangeForm.credits);
    window.$message?.success(`兑换成功，获得${res.data.amount}虚拟币`);
    showExchangeModal.value = false;
    exchangeForm.credits = 0;
    exchangeForm.amount = 0;
    getWalletInfo();
    getTransactions(true);
  } catch (error) {
    console.error('兑换失败', error);
  }
}

// 搜索
function handleSearch() {
  getTransactions(true);
}

// 重置筛选
function resetFilter() {
  filterForm.type = undefined;
  filterForm.status = undefined;
  filterForm.startTime = null;
  filterForm.endTime = null;
  handleSearch();
}

// 获取交易类型文本
function getTransactionTypeText(type: number) {
  switch (type) {
    case 0:
      return '充值';
    case 1:
      return '消费';
    case 2:
      return '收入';
    case 3:
      return '提现';
    case 4:
      return '退款';
    default:
      return '未知';
  }
}

// 获取交易状态文本
function getTransactionStatusText(status: number) {
  switch (status) {
    case 0:
      return '处理中';
    case 1:
      return '成功';
    case 2:
      return '失败';
    default:
      return '未知';
  }
}

// 获取状态标签类型
function getStatusType(status: number) {
  switch (status) {
    case 0:
      return 'warning';
    case 1:
      return 'success';
    case 2:
      return 'error';
    default:
      return 'default';
  }
}

// 分页变化
function handlePageChange(page: number) {
  pagination.page = page;
  getTransactions();
}

onMounted(() => {
  getWalletInfo();
  getTransactions();
});
</script>

<template>
  <div class="p-24px">
    <div class="flex gap-24px mb-24px">
      <!-- 钱包卡片 -->
      <NCard :bordered="false" class="flex-1 shadow-sm">
        <NSpin :show="loading">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="text-lg font-medium mb-8px">我的余额</div>
              <div class="text-4xl font-bold text-primary">{{ wallet?.balance?.toFixed(2) || '0.00' }}</div>
              <div class="mt-8px text-gray-500">
                可用: {{ availableBalance.toFixed(2) }}，冻结: {{ wallet?.frozenAmount?.toFixed(2) || '0.00' }}
              </div>
            </div>
            <div class="flex gap-12px">
              <NButton type="primary" @click="showRechargeModal = true">充值</NButton>
              <NButton @click="showWithdrawModal = true">提现</NButton>
              <NButton @click="showTransferModal = true">转账</NButton>
            </div>
          </div>
        </NSpin>
        </NCard>
      
      <!-- 积分卡片 -->
      <NCard :bordered="false" class="flex-1 shadow-sm">
        <NSpin :show="loading">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="text-lg font-medium mb-8px">我的积分</div>
              <div class="text-4xl font-bold text-success">{{ credits }}</div>
              <div class="mt-8px text-gray-500">
                完成任务、参与活动可获得积分
              </div>
            </div>
            <div class="flex gap-12px">
              <NButton type="success" @click="showExchangeModal = true">积分兑换</NButton>
              <NButton>积分规则</NButton>
            </div>
          </div>
        </NSpin>
        </NCard>
    </div>
      
      <!-- 交易记录 -->
    <NCard title="交易记录" :bordered="false" class="shadow-sm">
      <!-- 筛选条件 -->
      <div class="mb-16px">
        <NForm inline :model="filterForm">
          <NFormItem label="交易类型">
            <NSelect v-model:value="filterForm.type" style="width: 140px;" :options="[
              { label: '全部类型', value: undefined },
              { label: '充值', value: 0 },
              { label: '消费', value: 1 },
              { label: '收入', value: 2 },
              { label: '提现', value: 3 },
              { label: '退款', value: 4 }
            ]" />
          </NFormItem>
          
          <NFormItem label="交易状态">
            <NSelect v-model:value="filterForm.status" style="width: 140px;" :options="[
              { label: '全部状态', value: undefined },
              { label: '处理中', value: 0 },
              { label: '成功', value: 1 },
              { label: '失败', value: 2 }
            ]" />
          </NFormItem>
          
          <NFormItem label="交易时间">
            <NDatePicker
              v-model:value="filterForm.startTime"
              type="datetime"
              placeholder="开始时间"
              style="width: 160px;"
            />
            <span class="mx-8px">至</span>
            <NDatePicker
              v-model:value="filterForm.endTime"
              type="datetime"
              placeholder="结束时间"
              style="width: 160px;"
            />
          </NFormItem>
          
          <NFormItem>
            <NButton type="primary" @click="handleSearch">搜索</NButton>
            <NButton class="ml-12px" @click="resetFilter">重置</NButton>
          </NFormItem>
        </NForm>
      </div>
      
      <!-- 交易列表 -->
      <NSpin :show="transactionLoading">
        <NDataTable
          :columns="[
            {
              title: '序号',
              key: 'index',
              width: 80,
              render: (row, index) => index + 1 + (pagination.page - 1) * pagination.pageSize
            },
            { title: '交易类型', key: 'type', width: 100, render: (row) => getTransactionTypeText(row.type) },
            { 
              title: '交易金额', 
              key: 'amount', 
              width: 120,
              render: (row) => {
                const isIncome = row.type === 2 || row.type === 0 || row.type === 4;
                return h('span', { class: isIncome ? 'text-success' : 'text-error' }, 
                  `${isIncome ? '+' : '-'}${row.amount.toFixed(2)}`
                );
              }
            },
            { 
              title: '交易状态', 
              key: 'status', 
              width: 100,
              render: (row) => h(NTag, { type: getStatusType(row.status) }, { default: () => getTransactionStatusText(row.status) })
            },
            { title: '交易时间', key: 'createdTime', width: 180 },
            { title: '关联订单', key: 'relatedId', width: 180, render: (row) => row.relatedId ? `${row.relatedType || ''} #${row.relatedId}` : '-' },
            { title: '备注', key: 'remark', ellipsis: { tooltip: true } }
          ]"
          :data="transactions"
          :pagination="{
            page: pagination.page,
            pageSize: pagination.pageSize,
            pageCount: Math.ceil(pagination.total / pagination.pageSize),
            showSizePicker: true,
            pageSizes: [10, 20, 30, 50],
            onChange: handlePageChange,
            onUpdatePageSize: (pageSize) => {
              pagination.pageSize = pageSize;
              pagination.page = 1;
              getTransactions();
            }
          }"
          :bordered="false"
        />
        
        <NEmpty v-if="transactions.length === 0 && !transactionLoading" description="暂无交易记录" />
      </NSpin>
        </NCard>
    
    <!-- 充值弹窗 -->
    <NModal v-model:show="showRechargeModal" preset="card" title="充值" style="width: 400px;">
      <NForm :model="rechargeForm" label-placement="left" :label-width="100">
        <NFormItem label="充值金额">
          <NInputNumber v-model:value="rechargeForm.amount" :min="0" :precision="2" style="width: 100%;" />
        </NFormItem>
        
        <NFormItem label="支付方式">
          <NRadioGroup v-model:value="payMethod" name="payMethod" default-value="alipay">
            <NRadio value="alipay">支付宝</NRadio>
            <NRadio value="wechat">微信支付</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showRechargeModal = false">取消</NButton>
          <NButton type="primary" :disabled="rechargeForm.amount <= 0" @click="handleRecharge">
            确认充值
          </NButton>
        </div>
      </NForm>
    </NModal>
    
    <!-- 提现弹窗 -->
    <NModal v-model:show="showWithdrawModal" preset="card" title="提现" style="width: 400px;">
      <NForm :model="withdrawForm" label-placement="left" :label-width="100">
        <NFormItem label="可用余额">
          <span>{{ availableBalance.toFixed(2) }}</span>
        </NFormItem>
        
        <NFormItem label="提现金额">
          <NInputNumber v-model:value="withdrawForm.amount" :min="0" :max="availableBalance" :precision="2" style="width: 100%;" />
        </NFormItem>
        
        <NFormItem label="收款账户">
          <NInput v-model:value="withdrawForm.accountInfo" placeholder="请输入收款账户信息" />
        </NFormItem>
        
        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showWithdrawModal = false">取消</NButton>
          <NButton type="primary" 
            :disabled="withdrawForm.amount <= 0 || withdrawForm.amount > availableBalance || !withdrawForm.accountInfo" 
            @click="handleWithdraw"
          >
            确认提现
          </NButton>
        </div>
      </NForm>
    </NModal>
    
    <!-- 转账弹窗 -->
    <NModal v-model:show="showTransferModal" preset="card" title="转账" style="width: 400px;">
      <NForm :model="transferForm" label-placement="left" :label-width="100">
        <NFormItem label="可用余额">
          <span>{{ availableBalance.toFixed(2) }}</span>
        </NFormItem>
        
        <NFormItem label="转账对象" required>
          <NAutoComplete 
            v-model:value="transferForm.targetUserName" 
            placeholder="输入用户名、学号或邮箱"
            :options="[]"
          />
        </NFormItem>
        
        <NFormItem label="转账金额" required>
          <NInputNumber v-model:value="transferForm.amount" :min="0" :max="availableBalance" :precision="2" style="width: 100%;" />
        </NFormItem>
        
        <NFormItem label="转账备注">
          <NInput v-model:value="transferForm.remark" placeholder="请输入转账备注" />
        </NFormItem>
        
        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showTransferModal = false">取消</NButton>
          <NButton type="primary" 
            :disabled="!transferForm.targetUserId || transferForm.amount <= 0 || transferForm.amount > availableBalance" 
            @click="handleTransfer"
          >
            确认转账
          </NButton>
        </div>
      </NForm>
    </NModal>
    
    <!-- 积分兑换弹窗 -->
    <NModal v-model:show="showExchangeModal" preset="card" title="积分兑换" style="width: 400px;">
      <NForm :model="exchangeForm" label-placement="left" :label-width="100">
        <NFormItem label="我的积分">
          <span>{{ credits }}</span>
        </NFormItem>
        
        <NFormItem label="兑换积分" required>
          <NInputNumber v-model:value="exchangeForm.credits" :min="0" :max="credits" style="width: 100%;" @update:value="calculateExchangeAmount" />
        </NFormItem>
        
        <NFormItem label="兑换比例">
          <span>1积分 = 0.1虚拟币</span>
        </NFormItem>
        
        <NFormItem label="获得虚拟币">
          <span class="text-success font-bold">{{ exchangeForm.amount.toFixed(2) }}</span>
        </NFormItem>
        
        <div class="flex justify-end gap-12px mt-16px">
          <NButton @click="showExchangeModal = false">取消</NButton>
          <NButton type="primary" 
            :disabled="exchangeForm.credits <= 0 || exchangeForm.credits > credits" 
            @click="handleExchange"
          >
            确认兑换
          </NButton>
        </div>
      </NForm>
    </NModal>
  </div>
</template>

<style scoped>
.n-card :deep(.n-card-header) {
  padding-bottom: 8px;
}
</style> 