<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'Order'
});

const router = useRouter();

// 订单类型和状态
enum OrderType {
  BUY = 'buy',
  SELL = 'sell'
}

enum OrderStatus {
  PENDING = 'pending', // 待确认
  CONFIRMED = 'confirmed', // 已确认待服务
  ONGOING = 'ongoing', // 服务中
  COMPLETED = 'completed', // 已完成
  EVALUATED = 'evaluated', // 已评价
  CANCELLED = 'cancelled', // 已取消
  REFUNDING = 'refunding', // 退款中
  REFUNDED = 'refunded' // 已退款
}

// 活动标签页
const activeTab = ref(OrderType.BUY);

// 订单列表
const orders = reactive([
  {
    id: 1001,
    type: OrderType.BUY,
    status: OrderStatus.PENDING,
    title: 'Web前端开发指导',
    price: 50,
    quantity: 2,
    totalPrice: 100,
    orderTime: '2023-06-10 14:30:25',
    serviceTime: '2023-06-15 19:00:00',
    contactMethod: 'online',
    contactInfo: 'WeChat: user123',
    provider: {
      id: 101,
      name: '李明',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
    }
  },
  {
    id: 1002,
    type: OrderType.BUY,
    status: OrderStatus.CONFIRMED,
    title: '英语口语练习与纠音',
    price: 40,
    quantity: 1.5,
    totalPrice: 60,
    orderTime: '2023-06-08 10:15:32',
    serviceTime: '2023-06-12 14:00:00',
    contactMethod: 'offline',
    contactInfo: '理工校区图书馆一楼咖啡厅',
    provider: {
      id: 103,
      name: '王芳',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
    }
  },
  {
    id: 1003,
    type: OrderType.BUY,
    status: OrderStatus.COMPLETED,
    title: '数据结构与算法辅导',
    price: 60,
    quantity: 2,
    totalPrice: 120,
    orderTime: '2023-06-05 16:42:18',
    serviceTime: '2023-06-07 19:30:00',
    contactMethod: 'online',
    contactInfo: 'QQ: 123456789',
    provider: {
      id: 104,
      name: '赵强',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
    }
  },
  {
    id: 1004,
    type: OrderType.SELL,
    status: OrderStatus.PENDING,
    title: 'Python编程入门辅导',
    price: 55,
    quantity: 3,
    totalPrice: 165,
    orderTime: '2023-06-09 09:23:45',
    serviceTime: '2023-06-16 18:00:00',
    contactMethod: 'online',
    contactInfo: 'WeChat: student001',
    customer: {
      id: 201,
      name: '张三',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
    }
  },
  {
    id: 1005,
    type: OrderType.SELL,
    status: OrderStatus.CONFIRMED,
    title: 'Python编程入门辅导',
    price: 55,
    quantity: 2,
    totalPrice: 110,
    orderTime: '2023-06-07 14:12:36',
    serviceTime: '2023-06-13 15:30:00',
    contactMethod: 'offline',
    contactInfo: '文理校区教学楼B302',
    customer: {
      id: 202,
      name: '李四',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg'
    }
  }
]);

// 当前显示的订单列表
const currentOrders = computed(() => {
  return orders.filter(order => order.type === activeTab.value);
});

// 订单状态显示
function getStatusText(status: OrderStatus) {
  const statusMap: Record<OrderStatus, string> = {
    [OrderStatus.PENDING]: '待确认',
    [OrderStatus.CONFIRMED]: '待服务',
    [OrderStatus.ONGOING]: '服务中',
    [OrderStatus.COMPLETED]: '已完成',
    [OrderStatus.EVALUATED]: '已评价',
    [OrderStatus.CANCELLED]: '已取消',
    [OrderStatus.REFUNDING]: '退款中',
    [OrderStatus.REFUNDED]: '已退款'
  };
  return statusMap[status] || '未知状态';
}

// 获取订单状态类型
function getStatusType(status: OrderStatus) {
  const statusTypeMap: Record<OrderStatus, 'default' | 'info' | 'success' | 'warning' | 'error'> = {
    [OrderStatus.PENDING]: 'warning',
    [OrderStatus.CONFIRMED]: 'info',
    [OrderStatus.ONGOING]: 'info',
    [OrderStatus.COMPLETED]: 'success',
    [OrderStatus.EVALUATED]: 'success',
    [OrderStatus.CANCELLED]: 'default',
    [OrderStatus.REFUNDING]: 'warning',
    [OrderStatus.REFUNDED]: 'default'
  };
  return statusTypeMap[status] || 'default';
}

// 订单操作按钮配置
function getOrderActions(order: any) {
  if (order.type === OrderType.BUY) {
    switch (order.status) {
      case OrderStatus.PENDING:
        return [
          { label: '取消订单', type: 'default', action: () => cancelOrder(order.id) }
        ];
      case OrderStatus.CONFIRMED:
        return [
          { label: '取消订单', type: 'default', action: () => cancelOrder(order.id) },
          { label: '联系服务者', type: 'info', action: () => contactProvider(order.id) }
        ];
      case OrderStatus.ONGOING:
        return [
          { label: '申请退款', type: 'warning', action: () => refundOrder(order.id) },
          { label: '联系服务者', type: 'info', action: () => contactProvider(order.id) },
          { label: '确认完成', type: 'success', action: () => completeOrder(order.id) }
        ];
      case OrderStatus.COMPLETED:
        return [
          { label: '评价服务', type: 'primary', action: () => evaluateOrder(order.id) }
        ];
      default:
        return [];
    }
  } else {
    // 服务提供方的操作
    switch (order.status) {
      case OrderStatus.PENDING:
        return [
          { label: '拒绝订单', type: 'error', action: () => rejectOrder(order.id) },
          { label: '接受订单', type: 'success', action: () => acceptOrder(order.id) }
        ];
      case OrderStatus.CONFIRMED:
        return [
          { label: '联系客户', type: 'info', action: () => contactCustomer(order.id) },
          { label: '开始服务', type: 'primary', action: () => startService(order.id) }
        ];
      case OrderStatus.ONGOING:
        return [
          { label: '联系客户', type: 'info', action: () => contactCustomer(order.id) },
          { label: '完成服务', type: 'success', action: () => finishService(order.id) }
        ];
      default:
        return [];
    }
  }
}

// 订单操作方法
function cancelOrder(orderId: number) {
  window.$message?.success(`订单 ${orderId} 已取消`);
  const order = orders.find(o => o.id === orderId);
  if (order) order.status = OrderStatus.CANCELLED;
}

function contactProvider(orderId: number) {
  window.$message?.info(`已向服务提供者发送联系请求`);
}

function refundOrder(orderId: number) {
  window.$message?.info(`退款申请已提交，等待审核`);
  const order = orders.find(o => o.id === orderId);
  if (order) order.status = OrderStatus.REFUNDING;
}

function completeOrder(orderId: number) {
  window.$message?.success(`订单 ${orderId} 已完成`);
  const order = orders.find(o => o.id === orderId);
  if (order) order.status = OrderStatus.COMPLETED;
}

function evaluateOrder(orderId: number) {
  // 评价订单，显示评价对话框
  window.$message?.info(`评价功能开发中`);
}

function rejectOrder(orderId: number) {
  window.$message?.success(`已拒绝订单 ${orderId}`);
  const order = orders.find(o => o.id === orderId);
  if (order) order.status = OrderStatus.CANCELLED;
}

function acceptOrder(orderId: number) {
  window.$message?.success(`已接受订单 ${orderId}`);
  const order = orders.find(o => o.id === orderId);
  if (order) order.status = OrderStatus.CONFIRMED;
}

function contactCustomer(orderId: number) {
  window.$message?.info(`已向客户发送联系请求`);
}

function startService(orderId: number) {
  window.$message?.success(`服务已开始`);
  const order = orders.find(o => o.id === orderId);
  if (order) order.status = OrderStatus.ONGOING;
}

function finishService(orderId: number) {
  window.$message?.success(`服务已完成，等待客户确认`);
  // 实际操作中，这里不应立即将状态改为完成，而是等待客户确认
}

// 查看订单详情
function viewOrderDetail(orderId: number) {
  window.$message?.info(`查看订单详情: ${orderId}`);
  // 实际项目中跳转到订单详情页
  // router.push(`/order/${orderId}`);
}
</script>

<template>
  <div class="p-24px">
    <NCard :bordered="false" size="large" class="rounded-16px shadow-sm">
      <template #header>
        <div class="text-18px font-bold">订单管理</div>
      </template>
      
      <NTabs v-model:value="activeTab" type="segment" animated>
        <NTabPane name="buy" tab="我购买的服务">
          <div class="pt-16px">
            <NEmpty v-if="currentOrders.length === 0" description="暂无订单数据" />
            
            <div v-else class="space-y-16px">
              <NCard 
                v-for="order in currentOrders" 
                :key="order.id"
                :bordered="false"
                size="small"
                class="rounded-8px shadow-sm cursor-pointer hover:shadow-md transition-all duration-300"
                @click="viewOrderDetail(order.id)"
              >
                <div class="flex justify-between">
                  <div class="flex items-center">
                    <span class="text-14px text-gray-500">订单号: {{ order.id }}</span>
                    <span class="mx-8px text-gray-300">|</span>
                    <span class="text-14px text-gray-500">{{ order.orderTime }}</span>
                  </div>
                  <NTag :type="getStatusType(order.status)" size="small">
                    {{ getStatusText(order.status) }}
                  </NTag>
                </div>
                
                <div class="flex items-center mt-16px">
                  <div class="flex-1">
                    <div class="font-medium text-16px">{{ order.title }}</div>
                    <div class="mt-8px text-14px text-gray-500">
                      <div>服务时间: {{ order.serviceTime }}</div>
                      <div>联系方式: {{ order.contactMethod === 'online' ? '线上' : '线下' }}</div>
                      <div>联系信息: {{ order.contactInfo }}</div>
                    </div>
                  </div>
                  
                  <div class="ml-16px flex items-center">
                    <NAvatar
                      :src="order.provider.avatar"
                      round
                      size="small"
                    />
                    <div class="ml-8px">
                      <div class="text-14px">{{ order.provider.name }}</div>
                      <div class="text-12px text-gray-400">提供者</div>
                    </div>
                  </div>
                </div>
                
                <div class="flex justify-between items-center mt-16px pt-16px border-t border-gray-100">
                  <div>
                    <span class="text-14px text-gray-500">服务单价: {{ order.price }} 学币</span>
                    <span class="mx-8px text-gray-300">×</span>
                    <span class="text-14px text-gray-500">{{ order.quantity }} {{ order.quantity % 1 === 0 ? '小时' : '小时' }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-16px">
                      <span class="text-gray-500">合计: </span>
                      <span class="text-16px font-medium text-primary">{{ order.totalPrice }}</span>
                      <span class="text-gray-500"> 学币</span>
                    </span>
                    
                    <NSpace v-if="getOrderActions(order).length > 0">
                      <NButton 
                        v-for="(action, idx) in getOrderActions(order)" 
                        :key="idx"
                        :type="action.type"
                        size="small"
                        @click.stop="action.action()"
                      >
                        {{ action.label }}
                      </NButton>
                    </NSpace>
                  </div>
                </div>
              </NCard>
            </div>
          </div>
        </NTabPane>
        
        <NTabPane name="sell" tab="我提供的服务">
          <div class="pt-16px">
            <NEmpty v-if="currentOrders.length === 0" description="暂无订单数据" />
            
            <div v-else class="space-y-16px">
              <NCard 
                v-for="order in currentOrders" 
                :key="order.id"
                :bordered="false"
                size="small"
                class="rounded-8px shadow-sm cursor-pointer hover:shadow-md transition-all duration-300"
                @click="viewOrderDetail(order.id)"
              >
                <div class="flex justify-between">
                  <div class="flex items-center">
                    <span class="text-14px text-gray-500">订单号: {{ order.id }}</span>
                    <span class="mx-8px text-gray-300">|</span>
                    <span class="text-14px text-gray-500">{{ order.orderTime }}</span>
                  </div>
                  <NTag :type="getStatusType(order.status)" size="small">
                    {{ getStatusText(order.status) }}
                  </NTag>
                </div>
                
                <div class="flex items-center mt-16px">
                  <div class="flex-1">
                    <div class="font-medium text-16px">{{ order.title }}</div>
                    <div class="mt-8px text-14px text-gray-500">
                      <div>服务时间: {{ order.serviceTime }}</div>
                      <div>联系方式: {{ order.contactMethod === 'online' ? '线上' : '线下' }}</div>
                      <div>联系信息: {{ order.contactInfo }}</div>
                    </div>
                  </div>
                  
                  <div class="ml-16px flex items-center">
                    <NAvatar
                      :src="order.customer.avatar"
                      round
                      size="small"
                    />
                    <div class="ml-8px">
                      <div class="text-14px">{{ order.customer.name }}</div>
                      <div class="text-12px text-gray-400">客户</div>
                    </div>
                  </div>
                </div>
                
                <div class="flex justify-between items-center mt-16px pt-16px border-t border-gray-100">
                  <div>
                    <span class="text-14px text-gray-500">服务单价: {{ order.price }} 学币</span>
                    <span class="mx-8px text-gray-300">×</span>
                    <span class="text-14px text-gray-500">{{ order.quantity }} {{ order.quantity % 1 === 0 ? '小时' : '小时' }}</span>
                  </div>
                  <div class="flex items-center">
                    <span class="mr-16px">
                      <span class="text-gray-500">合计: </span>
                      <span class="text-16px font-medium text-primary">{{ order.totalPrice }}</span>
                      <span class="text-gray-500"> 学币</span>
                    </span>
                    
                    <NSpace v-if="getOrderActions(order).length > 0">
                      <NButton 
                        v-for="(action, idx) in getOrderActions(order)" 
                        :key="idx"
                        :type="action.type"
                        size="small"
                        @click.stop="action.action()"
                      >
                        {{ action.label }}
                      </NButton>
                    </NSpace>
                  </div>
                </div>
              </NCard>
            </div>
          </div>
        </NTabPane>
      </NTabs>
    </NCard>
  </div>
</template>

<style scoped></style> 