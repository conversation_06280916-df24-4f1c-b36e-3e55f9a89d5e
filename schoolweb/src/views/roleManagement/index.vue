<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMessage } from 'naive-ui';
import { useAuthStore } from '@/store/modules/auth';
import { createRole, deleteRole, addUsersToRole, removeUsersFromRole, deleteUserRoles, type RoleInfo, updateRole, getRoleUsers } from '@/service/api/role';
import { batchGetUserInfo } from '@/service/api/user';
import { ElMessageBox } from 'element-plus';
import { getRoleList } from '@/service/api/role';
import { ElMessage } from 'element-plus';
import { searchUsers } from '@/service/api/user';
import { Close } from '@element-plus/icons-vue';

const authStore = useAuthStore();
const isAdmin = computed(() => authStore.isAdmin);
const message = useMessage();

// 角色列表数据
const roleList = ref<RoleInfo[]>([]);
const loading = ref(false);

// 编辑对话框
const showEditDialog = ref(false);
const editingRole = ref<Partial<RoleInfo>>({});
const editMode = ref<'edit' | 'create'>('edit');

// 用户管理对话框
const showUserDialog = ref(false);
const currentRoleId = ref<number>();
const selectedUsers = ref<string[]>([]);
const userOptions = ref<Array<{
  value: string;
  label: string;
  name: string;
  stuId: string;
}>>([]);
const roleUsers = ref<{ userId: string; name: string; stuId: string }[]>([]);

// 加载角色列表
const loadRoles = async () => {
  if (!isAdmin.value) {
    message.error('只有管理员可以访问此页面');
    return;
  }

  loading.value = true;
  try {
    roleList.value = await getRoleList();
  } catch (error) {
    console.error('加载角色列表失败:', error);
    message.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 打开编辑对话框
const openEditDialog = (role?: RoleInfo) => {
  if (role) {
    editingRole.value = { ...role };
    editMode.value = 'edit';
  } else {
    editingRole.value = {};
    editMode.value = 'create';
  }
  showEditDialog.value = true;
};

// 保存角色
const saveRole = async () => {
  if (!editingRole.value.roleName) {
    message.error('角色名称不能为空');
    return;
  }

  try {
    if (editMode.value === 'edit' && editingRole.value.roleId) {
      await updateRole(editingRole.value.roleId, editingRole.value);
    } else {
      await createRole(editingRole.value);
    }
    message.success(editMode.value === 'edit' ? '更新成功' : '创建成功');
    showEditDialog.value = false;
    loadRoles();
  } catch (error) {
    console.error('保存角色失败:', error);
    message.error('保存失败');
  }
};

// 删除角色
const handleDeleteRole = async (role: RoleInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色"${role.roleName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await deleteRole(role.roleId);
    message.success('删除成功');
    loadRoles();
  } catch (error) {
    if (error !== 'cancel') {
      message.error('删除失败');
      console.error('删除角色错误:', error);
    }
  }
};

// 打开用户管理对话框
const openUserDialog = async (roleId: number) => {
  currentRoleId.value = roleId;
  showUserDialog.value = true;
  
  try {
    const users = await getRoleUsers(roleId);
    roleUsers.value = users;
  } catch (error) {
    console.error('加载角色用户列表失败:', error);
    message.error('获取用户列表失败');
  }
};

// 搜索用户
const handleSearchInput = async (query: string) => {
  if (!query || query.length < 2) {
    userOptions.value = [];
    return;
  }

  loading.value = true;
  try {
    const response = await searchUsers(query);
    if (response?.data) {
      userOptions.value = response.data.map(user => ({
        value: user.userId.toString(),
        label: `${user.name} (${user.stuId})`,
        name: user.name,
        stuId: user.stuId
      }));
    }
  } catch (error) {
    console.error('搜索用户失败:', error);
    ElMessage.error('搜索用户失败');
    userOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 清除用户选择
const clearUsers = () => {
  selectedUsers.value = [];
};

// 添加用户到角色
const addUsers = async () => {
  if (!selectedUsers.value.length || !currentRoleId.value) return;

  try {
    const success = await addUsersToRole(currentRoleId.value, selectedUsers.value.map(Number));
    // console.log("1111"+code);
    if (success) {
      ElMessage.success('添加用户成功');
      selectedUsers.value = [];
      const users = await getRoleUsers(currentRoleId.value);
      roleUsers.value = users;
    }
  } catch (error) {
    console.error('添加用户失败:', error);
    ElMessage.error('添加用户失败');
  }
};

// 从角色中移除用户
const removeUser = async (userId: string) => {
  if (!currentRoleId.value) return;
  
  try {
    const success = await removeUsersFromRole([Number(userId)], currentRoleId.value);
    // console.log("1111"+success);
    if (success) {
      ElMessage.success('移除用户成功');
      const users = await getRoleUsers(currentRoleId.value);
      roleUsers.value = users;
    }
  } catch (error) {
    console.error('移除用户失败:', error);
    ElMessage.error('移除用户失败');
  }
};

// 页面加载时获取角色列表
onMounted(() => {
  if (isAdmin.value) {
    loadRoles();
  }
});
</script>

<template>
  <div v-if="isAdmin" class="role-management-page">
    <div class="page-header">
      <h2>角色管理</h2>
      <div class="operation-bar">
        <el-button type="primary" @click="openEditDialog()">
          创建角色
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="roleList"
      border
      style="width: 100%"
    >
      <el-table-column prop="roleName" label="角色名称" width="150" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="createdTime" label="创建时间" width="180">
        <template #default="{ row }">
          {{ row.createdTime ? new Date(row.createdTime).toLocaleString() : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updatedTime" label="更新时间" width="180">
        <template #default="{ row }">
          {{ row.updatedTime ? new Date(row.updatedTime).toLocaleString() : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="200">
        <template #default="{ row }">
          <el-button-group>
            <el-button
              size="small"
              type="primary"
              @click="openEditDialog(row)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="info"
              @click="openUserDialog(row.roleId)"
            >
              用户管理
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDeleteRole(row)"
            >
              删除
            </el-button>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑角色对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="editMode === 'edit' ? '编辑角色' : '创建角色'"
      width="500px"
    >
      <el-form :model="editingRole" label-width="100px">
        <el-form-item label="角色名称">
          <el-input v-model="editingRole.roleName" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="editingRole.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveRole">保存</el-button>
      </template>
    </el-dialog>

    <!-- 用户管理对话框 -->
    <div v-if="showUserDialog" class="role-management">
      <div class="role-user-dialog">
        <div class="dialog-header">
          <h2>角色用户管理</h2>
          <el-button class="close-button" @click="showUserDialog = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <div class="search-section">
          <el-select
            v-model="selectedUsers"
            multiple
            filterable
            remote
            :remote-method="handleSearchInput"
            :loading="loading"
            placeholder="搜索并选择要添加的用户"
            class="user-select"
            clearable
            @clear="clearUsers"
            :no-data-text="loading ? '加载中...' : '无数据'"
            :reserve-keyword="false"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <div class="suggestion-item">
                <div class="name">{{ item.name }}</div>
                <div class="stuid">{{ item.stuId }}</div>
              </div>
            </el-option>
          </el-select>
          <el-button type="primary" @click="addUsers" :disabled="!selectedUsers.length">
            添加用户
          </el-button>
        </div>

        <div class="user-list">
          <el-table :data="roleUsers" style="width: 100%">
            <el-table-column prop="name" label="姓名" width="180">
              <template #default="{ row }">
                <div class="user-info">
                  <span class="user-name">{{ row.name }}</span>
                  <el-tag size="small" type="info" class="user-id-tag">{{ row.stuId }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button 
                  type="danger" 
                  size="small"
                  @click="removeUser(row.userId)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="unauthorized">
    <h2>未授权访问</h2>
    <p>只有管理员可以访问此页面</p>
  </div>
</template>

<style scoped>
.role-management-page {
  padding: 20px;
  background: #fff;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.operation-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.unauthorized {
  text-align: center;
  padding: 50px;
  color: #666;
}

.unauthorized h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-button-group) {
  display: flex;
  gap: 5px;
}

.mb-4 {
  margin-bottom: 16px;
}

.ml-2 {
  margin-left: 8px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 10px;
  }

  :deep(.el-table) {
    width: 100%;
    overflow-x: auto;
  }

  .el-button-group {
    flex-wrap: wrap;
  }
}

.role-management {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.role-user-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.close-button {
  padding: 8px;
  border: none;
}

.search-section {
  padding: 16px 20px;
  display: flex;
  gap: 12px;
  border-bottom: 1px solid #ebeef5;
}

.user-select {
  flex: 1;
  min-width: 0;
}

.user-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.suggestion-item .name {
  font-weight: 500;
}

.suggestion-item .stuid {
  color: #909399;
  font-size: 13px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
}

.user-id-tag {
  font-size: 12px;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-select-dropdown__item) {
  padding: 0 12px;
}

@media (max-width: 768px) {
  .role-user-dialog {
    width: 95%;
    max-height: 90vh;
  }

  .search-section {
    flex-direction: column;
  }

  .user-select {
    width: 100%;
  }
}
</style> 