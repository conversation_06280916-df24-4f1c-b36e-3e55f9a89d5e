<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { fetchCreditScore, fetchCreditRecords, fetchCreditRules } from '@/service/api';
import type { CreditScore, CreditRecord } from '@/service/api/credit';

defineOptions({
  name: 'UserCredit'
});

// 信用评分
const creditScore = ref<CreditScore | null>(null);
const loading = ref(false);

// 信用记录
const creditRecords = ref<CreditRecord[]>([]);
const recordsLoading = ref(false);
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

// 信用规则
const creditRules = ref({
  levels: [] as { level: string; min: number; max: number; description: string }[],
  rules: [] as { type: number; name: string; score: number; description: string }[]
});
const rulesLoading = ref(false);

// 获取信用评分
async function getCreditScore() {
  loading.value = true;
  try {
    const res = await fetchCreditScore();
    creditScore.value = res.data;
  } catch (error) {
    console.error('获取信用评分失败', error);
  } finally {
    loading.value = false;
  }
}

// 获取信用记录
async function getCreditRecords(page = 1) {
  recordsLoading.value = true;
  pagination.value.page = page;
  
  try {
    const res = await fetchCreditRecords({
      page: pagination.value.page,
      pageSize: pagination.value.pageSize
    });
    
    if (res.data?.records) {
      creditRecords.value = res.data.records;
      pagination.value.total = res.data.total;
    }
  } catch (error) {
    console.error('获取信用记录失败', error);
  } finally {
    recordsLoading.value = false;
  }
}

// 获取信用规则
async function getCreditRules() {
  rulesLoading.value = true;
  try {
    const res = await fetchCreditRules();
    creditRules.value = res.data;
  } catch (error) {
    console.error('获取信用规则失败', error);
  } finally {
    rulesLoading.value = false;
  }
}

// 获取变更类型文本
function getChangeTypeText(type: number) {
  switch (type) {
    case 0:
      return '初始化';
    case 1:
      return '订单完成';
    case 2:
      return '被评价';
    case 3:
      return '评价他人';
    case 4:
      return '取消订单';
    case 5:
      return '拒绝订单';
    case 6:
      return '违规';
    default:
      return '未知';
  }
}

// 获取信用等级颜色
function getCreditLevelColor(level: string) {
  switch (level) {
    case 'A+':
      return '#18a058';
    case 'A':
      return '#2080f0';
    case 'B+':
      return '#12b7f5';
    case 'B':
      return '#f0a020';
    case 'C':
      return '#d03050';
    case 'D':
      return '#666666';
    default:
      return '#2080f0';
  }
}

// 信用等级进度
function getCreditProgress() {
  if (!creditScore.value) return 0;
  
  const currentLevel = creditRules.value.levels.find(level => level.level === creditScore.value?.level);
  if (!currentLevel) return 0;
  
  const { min, max } = currentLevel;
  const score = creditScore.value.score;
  
  return Math.min(100, Math.max(0, ((score - min) / (max - min)) * 100));
}

// 页面跳转
function handlePageChange(page: number) {
  getCreditRecords(page);
}

onMounted(() => {
  getCreditScore();
  getCreditRecords();
  getCreditRules();
});
</script>

<template>
  <div class="p-24px">
    <!-- 信用评分卡片 -->
    <NCard :bordered="false" class="shadow-sm mb-24px">
      <NSpin :show="loading">
        <div v-if="creditScore" class="flex gap-24px">
          <!-- 信用分数 -->
          <div class="flex-1 flex flex-col items-center justify-center">
            <div class="text-center mb-16px">
              <div class="text-4xl font-bold mb-8px" :style="{ color: getCreditLevelColor(creditScore.level) }">
                {{ creditScore.score }}
              </div>
              <div class="text-xl font-medium" :style="{ color: getCreditLevelColor(creditScore.level) }">
                {{ creditScore.level }}级信用
              </div>
            </div>
            
            <NProgress
              type="circle"
              :percentage="getCreditProgress()"
              :color="getCreditLevelColor(creditScore.level)"
              :rail-color="`${getCreditLevelColor(creditScore.level)}20`"
              :stroke-width="10"
              :show-indicator="false"
              class="mb-16px"
            />
            
            <div class="text-gray-500 text-sm">上次更新: {{ creditScore.lastUpdatedTime }}</div>
          </div>
          
          <!-- 信用说明 -->
          <div class="flex-1 flex flex-col">
            <div class="text-xl font-medium mb-16px">信用等级说明</div>
            
            <div class="mb-12px">
              <div class="font-medium">{{ creditScore.level }} - {{ creditScore.levelDescription }}</div>
              <div class="text-gray-500 mt-4px text-sm">
                {{ 
                  creditRules.levels.find(level => level.level === creditScore.level)?.description || 
                  '根据你的行为综合评定的信用等级，会影响你可以使用的功能和权限。'
                }}
              </div>
            </div>
            
            <div class="flex-1"></div>
            
            <NAlert type="info" title="提示" :bordered="false">
              <template #icon>
                <NIcon>
                  <div class="i-material-symbols:info"></div>
                </NIcon>
              </template>
              完成订单、诚信履约可提升信用分，取消订单、违约等行为会降低信用分。
            </NAlert>
          </div>
        </div>
        
        <NEmpty v-else description="暂无信用评分数据" />
      </NSpin>
    </NCard>
    
    <!-- 信用规则与记录 -->
    <div class="flex gap-24px">
      <!-- 信用规则 -->
      <NCard title="信用规则" :bordered="false" class="shadow-sm flex-1">
        <NSpin :show="rulesLoading">
          <NTabs type="line">
            <NTabPane name="credit-levels" tab="信用等级">
              <NTable :bordered="false" :single-line="false">
                <thead>
                  <tr>
                    <th>等级</th>
                    <th>分数范围</th>
                    <th>等级说明</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="level in creditRules.levels" :key="level.level">
                    <td>
                      <NTag :color="getCreditLevelColor(level.level)">{{ level.level }}</NTag>
                    </td>
                    <td>{{ level.min }} - {{ level.max }}</td>
                    <td>{{ level.description }}</td>
                  </tr>
                </tbody>
              </NTable>
            </NTabPane>
            
            <NTabPane name="score-rules" tab="积分规则">
              <NTable :bordered="false" :single-line="false">
                <thead>
                  <tr>
                    <th>行为</th>
                    <th>积分变化</th>
                    <th>说明</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="rule in creditRules.rules" :key="rule.type">
                    <td>{{ rule.name }}</td>
                    <td>
                      <NTag :type="rule.score > 0 ? 'success' : 'error'">
                        {{ rule.score > 0 ? '+' : '' }}{{ rule.score }}
                      </NTag>
                    </td>
                    <td>{{ rule.description }}</td>
                  </tr>
                </tbody>
              </NTable>
            </NTabPane>
          </NTabs>
        </NSpin>
      </NCard>
      
      <!-- 信用记录 -->
      <NCard title="信用记录" :bordered="false" class="shadow-sm flex-1">
        <NSpin :show="recordsLoading">
          <NList>
            <NEmpty v-if="creditRecords.length === 0" description="暂无信用记录" />
            <NListItem v-for="record in creditRecords" :key="record.id">
              <div class="flex justify-between">
                <div>
                  <div class="font-medium">{{ getChangeTypeText(record.changeType) }}</div>
                  <div class="text-gray-500 text-sm">{{ record.createdTime }}</div>
                  <div v-if="record.remark" class="text-gray-500 text-sm mt-4px">{{ record.remark }}</div>
                </div>
                <div>
                  <NTag :type="record.changeScore > 0 ? 'success' : 'error'" size="small">
                    {{ record.changeScore > 0 ? '+' : '' }}{{ record.changeScore }}
                  </NTag>
                </div>
              </div>
            </NListItem>
          </NList>
          
          <div class="flex justify-center mt-16px">
            <NPagination
              v-model:page="pagination.page"
              v-model:page-size="pagination.pageSize"
              :item-count="pagination.total"
              :page-sizes="[10, 20, 30, 50]"
              show-size-picker
              @update:page="handlePageChange"
            />
          </div>
        </NSpin>
      </NCard>
    </div>
  </div>
</template>

<style scoped>
.n-progress.n-progress--circle {
  width: 160px;
  height: 160px;
}
</style> 