<script setup lang="ts">
import { computed, onMounted, h } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { useRouteStore } from '@/store/modules/route';
import HeaderBanner from './modules/header-banner.vue';
import CardData from './modules/card-data.vue';
import LineChart from './modules/line-chart.vue';
import PieChart from './modules/pie-chart.vue';
import ProjectNews from './modules/project-news.vue';
import CreativityBanner from './modules/creativity-banner.vue';

const appStore = useAppStore();
const authStore = useAuthStore();
const routeStore = useRouteStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// 在页面加载时，如果是管理员，添加审核管理菜单
onMounted(() => {
  if (authStore.isAdmin) {
    if (!routeStore.menus.some(menu => menu.key === 'audit-management')) {
      const auditManagementMenu = {
        key: 'audit-management',
        label: '审核管理',
        routeKey: undefined as unknown as App.Global.RouteKey,
        routePath: '/admin/audit' as unknown as App.Global.RoutePath,
        icon: () => h('i', { class: 'i-mdi:clipboard-check text-16px' }),
      };
      
      routeStore.menus.push(auditManagementMenu);
    }
  }
});
</script>

<template>
  <NSpace vertical :size="16">
    <!-- <NAlert :title="$t('common.warning')" type="warning">
      {{ $t('page.home.branchDesc') }}
    </NAlert> -->
    <HeaderBanner />
    <CardData />
    <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:14">
        <NCard :bordered="false" class="card-wrapper">
          <LineChart />
        </NCard>
      </NGi>
      <NGi span="24 s:24 m:10">
        <NCard :bordered="false" class="card-wrapper">
          <PieChart />
        </NCard>
      </NGi>
    </NGrid>
    <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:14">
        <ProjectNews />
      </NGi>
      <NGi span="24 s:24 m:10">
        <CreativityBanner />
      </NGi>
    </NGrid>
  </NSpace>
</template>

<style scoped></style>
