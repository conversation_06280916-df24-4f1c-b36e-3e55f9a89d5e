<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMessage } from 'naive-ui';
import { useAuthStore } from '@/store/modules/auth';
import { fetchAllUsers, updateUser, deleteUser, resetUserPassword, type UserInfo, createUserByExcel, fetchAllRoles } from '@/service/api/user';
import { uploadFile, getFileUrl } from '@/service/api/file';
import { FilePurpose } from '@/enums/filePurpose';
import { ElMessageBox } from 'element-plus';
import { Plus, View, Hide, Upload } from '@element-plus/icons-vue';
import type { UploadRequestOptions } from 'element-plus';

const authStore = useAuthStore();
const isAdmin = computed(() => authStore.isAdmin);
const message = useMessage();

// 表格数据
const tableData = ref<UserInfo[]>([]);
const total = ref(0);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(25);
const searchQuery = ref('');
const searchStatus = ref(1);

// 编辑对话框
const showEditDialog = ref(false);
const editingUser = ref<Partial<UserInfo>>({});
const editMode = ref<'edit' | 'create'>('edit');

// 状态选项
const labStatusOptions = [
  { label: '正常', value: 1 },
  { label: '待认证', value: 2 },
  { label: '已开除', value: 3 },
  { label: '已退出', value: 4 },
  { label: '已毕业', value: 5 }
];

// 获取状态标签
const getLabStatusLabel = (status: number) => {
  return labStatusOptions.find(option => option.value === status)?.label || '未知';
};

// 获取状态类型
const getLabStatusType = (status: number) => {
  switch (status) {
    case 1: return 'success';
    case 2: return 'warning';
    case 3: return 'danger';
    case 4: return 'info';
    case 5: return '';
    default: return 'info';
  }
};

// 计算头像和一卡通照片的完整URL
const avatarUrl = computed(() => getFileUrl(editingUser.value.avatar || ''));
const cardPhotoUrl = computed(() => getFileUrl(editingUser.value.cardPhoto || ''));

// 新密码相关
const newPassword = ref('');
const showPassword = ref(false);
const enablePasswordChange = ref(false);

// 生成随机密码
const generateRandomPassword = () => {
  if (!enablePasswordChange.value) {
    enablePasswordChange.value = true;
  }
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const special = '!@#$%^&*()_+-=[]{}|;:,.<>?';

  // 确保每种字符至少有一个
  let password = '';
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += special[Math.floor(Math.random() * special.length)];

  // 剩余6个字符随机生成
  const allChars = uppercase + lowercase + numbers + special;
  for (let i = 0; i < 6; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // 打乱密码字符顺序
  password = password.split('').sort(() => Math.random() - 0.5).join('');

  newPassword.value = password;
};

// 加载用户列表
const loadUsers = async () => {
  if (!isAdmin.value) {
    message.error('只有管理员可以访问此页面');
    return;
  }

  loading.value = true;
  try {
    const pageResult = await fetchAllUsers({
      page: currentPage.value,
      pageSize: pageSize.value,
      query: searchQuery.value,
      status: searchStatus.value
    });

    if (pageResult) {
      tableData.value = pageResult.records || [];
      total.value = pageResult.total || 0;
      currentPage.value = pageResult.current || 1;
      pageSize.value = pageResult.size || 25;
    }
  } catch (error) {
    message.error('获取用户列表失败');
    console.error('获取用户列表错误:', error);
  } finally {
    loading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadUsers();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadUsers();
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadUsers();
};

// 打开编辑对话框
const openEditDialog = (user?: UserInfo) => {
  if (user) {
    editingUser.value = { ...user };
    editMode.value = 'edit';
  } else {
    editingUser.value = {};
    editMode.value = 'create';
  }
  newPassword.value = '';
  enablePasswordChange.value = false;
  showEditDialog.value = true;
  // 加载角色列表
  if (!allRoles.value.length) {
    loadRoles();
  }
};

// 关闭对话框时也清除新密码显示
const closeDialog = () => {
  showEditDialog.value = false;
  newPassword.value = '';
  enablePasswordChange.value = false;
};

// 保存用户信息
const saveUser = async () => {
  try {
    if (editMode.value === 'edit' && editingUser.value.userId) {
      // 创建更新数据对象，只包含需要的字段
      const {
        userId,
        name,
        stuId,
        cardId,
        academy,
        email,
        labStatus,
        avatar,
        cardPhoto,
        roles
      } = editingUser.value;

      const updateData = {
        userId,
        name,
        stuId,
        cardId,
        academy,
        email,
        labStatus,
        avatar,
        cardPhoto,
        roles
      };

      // 只有在启用密码修改且有新密码时才添加password字段
      if (enablePasswordChange.value && newPassword.value) {
        (updateData as any).password = newPassword.value;
      }

      await updateUser(updateData);
      message.success('更新成功');
      showEditDialog.value = false;
      loadUsers();
    }
  } catch (error) {
    message.error('保存失败');
    console.error('保存用户信息错误:', error);
  }
};

// 处理头像上传
const handleAvatarUpload = async (options: UploadRequestOptions) => {
  try {
    const file = options.file;
    const fileUrl = await uploadFile(file, FilePurpose.AVATAR);
    if (fileUrl) {
      editingUser.value.avatar = fileUrl;
      message.success('头像上传成功');
    } else {
      message.error('上传失败：未获取到文件URL');
    }
  } catch (error) {
    console.error('Upload error:', error);
    message.error('头像上传失败');
  }
};

// 处理一卡通照片上传
const handleCardPhotoUpload = async (options: UploadRequestOptions) => {
  try {
    const file = options.file;
    const fileUrl = await uploadFile(file, FilePurpose.CARD);
    if (fileUrl) {
      editingUser.value.cardPhoto = fileUrl;
      message.success('一卡通照片上传成功');
    } else {
      message.error('上传失败：未获取到文件URL');
    }
  } catch (error) {
    console.error('Upload error:', error);
    message.error('一卡通照片上传失败');
  }
};

// 导入Excel相关
const importLoading = ref(false);
const downloadUrl = ref('');

// 处理Excel导入
const handleImportExcel = async (options: UploadRequestOptions) => {
  try {
    importLoading.value = true;
    const file = options.file;
    const filePath = await createUserByExcel(file);

    if (filePath) {
      message.success('导入成功');
      downloadUrl.value = getFileUrl(filePath);
      // 刷新用户列表
      loadUsers();
    } else {
      message.error('导入失败：未知错误');
    }
  } catch (error) {
    console.error('Import error:', error);
    message.error('导入失败');
  } finally {
    importLoading.value = false;
  }
};

// 角色管理相关
const allRoles = ref<string[]>([]);
const showRoleDialog = ref(false);
const editingRoles = ref<string[]>([]);
const currentEditingUserId = ref<number>();

// 获取所有角色
const loadRoles = async () => {
  try {
    const response = await fetchAllRoles();
    if (response) {
      allRoles.value = response;
    }
  } catch (error) {
    message.error('获取角色列表失败');
    console.error('获取角色列表错误:', error);
  }
};

// 页面加载时获取用户列表
onMounted(() => {
  if (isAdmin.value) {
    loadUsers();
    loadRoles(); // 加载角色列表
  }
});
</script>

<template>
  <div v-if="isAdmin" class="user-management-page">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="operation-bar">
        <el-upload
          class="excel-uploader"
          :auto-upload="true"
          :show-file-list="false"
          accept=".xlsx,.xls"
          :http-request="handleImportExcel"
          :disabled="importLoading"
        >
          <el-button :loading="importLoading" type="primary">
            <el-icon class="mr-2"><Upload /></el-icon>
            导入用户Excel
          </el-button>
        </el-upload>
        <el-link
          v-if="downloadUrl"
          type="primary"
          :href="downloadUrl"
          target="_blank"
          class="ml-4"
        >
          下载导入结果
        </el-link>
        <div class="search-bar">
          <el-input
            v-model="searchQuery"
            placeholder="搜索用户名/学号/邮箱"
            clearable
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #append>
              <el-button @click="handleSearch">搜索</el-button>
            </template>
          </el-input>
          <el-select
            v-model="searchStatus"
            placeholder="选择状态"
            class="status-select"
            @change="handleSearch"
          >
            <el-option
              v-for="option in labStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="stuId" label="学号" width="120" />
      <el-table-column prop="cardId" label="一卡通号" width="120" />
      <el-table-column prop="academy" label="学院" width="180" />
      <el-table-column prop="email" label="邮箱" width="200" />
      <el-table-column label="角色" width="120">
        <template #default="{ row }">
          <el-tag
            v-for="role in row.roles"
            :key="role"
            class="mx-1"
            type="info"
          >
            {{ role }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="labStatus" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getLabStatusType(row.labStatus)">
            {{ getLabStatusLabel(row.labStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间" width="180">
        <template #default="{ row }">
          {{ row.createdTime ? new Date(row.createdTime).toLocaleString() : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="updatedTime" label="更新时间" width="180">
        <template #default="{ row }">
          {{ row.updatedTime ? new Date(row.updatedTime).toLocaleString() : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="lastLoginTime" label="最后登录时间" width="180" />
      <el-table-column label="操作" fixed="right" width="120">
        <template #default="{ row }">
          <el-button
            size="small"
            type="primary"
            @click="openEditDialog(row)"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[25, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
        :pager-count="7"
        prev-text="上一页"
        next-text="下一页"
        total-text="共"
        page-size-text="条/页"
        jumper-text="前往"
      />
    </div>

    <el-dialog
      v-model="showEditDialog"
      :title="editMode === 'edit' ? '编辑用户' : '创建用户'"
      width="600px"
    >
      <el-form :model="editingUser" label-width="100px">
        <div class="flex gap-8 mb-4">
          <el-form-item label="头像">
            <div class="avatar-upload-container">
              <el-upload
                class="avatar-uploader"
                :http-request="handleAvatarUpload"
                :show-file-list="false"
                accept="image/*"
              >
                <img
                  v-if="editingUser.avatar"
                  :src="avatarUrl"
                  class="avatar-image"
                />
                <el-icon v-else class="avatar-uploader-icon">
                  <Plus />
                </el-icon>
              </el-upload>
            </div>
          </el-form-item>

          <el-form-item label="一卡通照片">
            <div class="card-upload-container">
              <el-upload
                class="card-uploader"
                :http-request="handleCardPhotoUpload"
                :show-file-list="false"
                accept="image/*"
              >
                <img
                  v-if="editingUser.cardPhoto"
                  :src="cardPhotoUrl"
                  class="card-photo"
                />
                <el-icon v-else class="card-uploader-icon">
                  <Plus />
                </el-icon>
              </el-upload>
            </div>
          </el-form-item>
        </div>

        <el-form-item label="姓名">
          <el-input v-model="editingUser.name" />
        </el-form-item>
        <el-form-item label="学号">
          <el-input v-model="editingUser.stuId" />
        </el-form-item>
        <el-form-item label="一卡通号">
          <el-input v-model="editingUser.cardId" />
        </el-form-item>
        <el-form-item label="学院">
          <el-input v-model="editingUser.academy" />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="editingUser.email" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="editingUser.labStatus">
            <el-option
              v-for="option in labStatusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="角色">
          <el-select
            v-model="editingUser.roles"
            placeholder="请选择角色"
            multiple
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option
              v-for="role in allRoles"
              :key="role"
              :label="role"
              :value="role"
            />
          </el-select>
        </el-form-item>

        <el-divider content-position="left">密码设置</el-divider>

        <el-form-item label="修改密码">
          <el-switch v-model="enablePasswordChange" />
        </el-form-item>

        <el-form-item label="新密码" v-if="enablePasswordChange">
          <div class="flex items-center gap-4 password-section">
            <el-input
              v-model="newPassword"
              :type="showPassword ? 'text' : 'password'"
              placeholder="请输入新密码"
              class="flex-1"
            >
              <template #append>
                <el-button @click="showPassword = !showPassword">
                  <el-icon>
                    <component :is="showPassword ? View : Hide" />
                  </el-icon>
                </el-button>
              </template>
            </el-input>
            <el-button @click="generateRandomPassword" type="info">
              随机生成
            </el-button>
          </div>
          <div class="password-requirements text-sm text-gray-600 mt-2">
            密码要求：10位字符，包含大小写字母、数字和特殊符号
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveUser">保存</el-button>
      </template>
    </el-dialog>
  </div>
  <div v-else class="unauthorized">
    <h2>未授权访问</h2>
    <p>只有管理员可以访问此页面</p>
  </div>
</template>

<style scoped>
.user-management-page {
  padding: 20px;
  background: #fff;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.operation-bar {
  display: flex;
  align-items: center;
  gap: 16px;
}

.excel-uploader {
  display: inline-block;
}

.search-bar {
  display: flex;
  gap: 10px;
}

.search-input {
  width: 300px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.unauthorized {
  text-align: center;
  padding: 50px;
  color: #666;
}

.unauthorized h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

:deep(.el-table) {
  margin-top: 20px;
}

:deep(.el-button-group) {
  display: flex;
  gap: 5px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 10px;
  }

  .search-bar {
    width: 100%;
  }

  .search-input {
    width: 100%;
  }

  :deep(.el-table) {
    width: 100%;
    overflow-x: auto;
  }

  .el-button-group {
    flex-wrap: wrap;
  }
}

/* 添加全局样式以覆盖 Element Plus 的默认文本 */
.el-pagination {
  --el-pagination-button-disabled-bg-color: var(--el-disabled-bg-color);
  --el-pagination-button-disabled-color: var(--el-text-color-placeholder);
  --el-pagination-button-bg-color: var(--el-fill-color);
  --el-pagination-button-color: var(--el-text-color);
  --el-pagination-hover-color: var(--el-color-primary);
}

.el-pagination .el-select .el-input .el-input__inner {
  padding-right: 25px;
}

.el-pagination .el-select .el-input {
  margin: 0 5px;
  width: 100px;
}

.el-pagination button {
  background-color: transparent;
}

.el-pagination .btn-prev .el-icon,
.el-pagination .btn-next .el-icon {
  width: 12px;
  font-size: 12px;
}

.el-pagination .el-pager li {
  background-color: transparent;
}

.el-pagination .el-pager li.is-active {
  color: var(--el-color-primary);
  font-weight: bold;
}

.el-pagination .el-pagination__jump {
  margin-left: 0;
}

.el-pagination .el-pagination__editor.el-input {
  width: 50px;
}

.el-pagination .el-pagination__total {
  margin-right: 10px;
}

.avatar-upload-container {
  width: 100px;
  height: 100px;
}

.card-upload-container {
  width: 164px;  /* 保持1716:1050的比例，缩放到合适大小 */
  height: 100px;
}

.avatar-uploader,
.card-uploader {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration);
}

.avatar-uploader:hover,
.card-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon,
.card-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
}

.card-photo {
  width: 164px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
}

:deep(.el-upload) {
  width: 100%;
  height: 100%;
}

:deep(.el-upload-dragger) {
  width: 100%;
  height: 100%;
}

.text-sm {
  font-size: 14px;
}

.text-gray-600 {
  color: #666;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-4 {
  gap: 16px;
}

.password-section {
  width: 100%;
}

.password-requirements {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.mt-2 {
  margin-top: 8px;
}

.flex-1 {
  flex: 1;
}

.mr-2 {
  margin-right: 8px;
}

.ml-4 {
  margin-left: 16px;
}

.status-select {
  width: 120px;
  margin-left: 10px;
}
</style>
