<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useThemeStore } from '@/store/modules/theme';

defineOptions({
  name: 'Skill'
});

const router = useRouter();
const themeStore = useThemeStore();

// 搜索相关
const searchValue = ref('');
const showSearchPanel = ref(false);
const selectedCategories = ref<string[]>([]);
const priceRange = ref([0, 1000]);

const categoryOptions = [
  { label: '编程开发', value: 'programming' },
  { label: '设计创意', value: 'design' },
  { label: '语言翻译', value: 'translation' },
  { label: '音乐才艺', value: 'music' },
  { label: '学业辅导', value: 'tutoring' },
  { label: '摄影摄像', value: 'photography' },
  { label: '写作编辑', value: 'writing' },
  { label: '其他技能', value: 'others' }
];

// 模拟技能服务数据
const skillList = reactive([
  {
    id: 1,
    title: 'Web前端开发指导',
    description: '提供HTML、CSS、JavaScript等前端技术的一对一辅导，帮助你掌握前端开发技能。',
    price: 50,
    unit: '小时',
    category: 'programming',
    tags: ['HTML', 'CSS', 'JavaScript'],
    rating: 4.8,
    reviewCount: 24,
    provider: {
      id: 101,
      name: '李明',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      department: '计算机科学学院',
      creditScore: 95
    }
  },
  {
    id: 2,
    title: '平面设计与海报制作',
    description: '专业的平面设计服务，包括海报、宣传单页、Logo等设计，让你的创意变为现实。',
    price: 100,
    unit: '次',
    category: 'design',
    tags: ['Photoshop', '平面设计', '海报'],
    rating: 4.6,
    reviewCount: 15,
    provider: {
      id: 102,
      name: '张华',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      department: '艺术设计学院',
      creditScore: 92
    }
  },
  {
    id: 3,
    title: '英语口语练习与纠音',
    description: '英语专业学生提供的口语练习服务，纠正发音问题，提高英语口语表达能力。',
    price: 40,
    unit: '小时',
    category: 'translation',
    tags: ['英语口语', '发音纠正', '外语学习'],
    rating: 4.9,
    reviewCount: 32,
    provider: {
      id: 103,
      name: '王芳',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      department: '外国语学院',
      creditScore: 97
    }
  },
  {
    id: 4,
    title: '数据结构与算法辅导',
    description: '针对计算机专业的数据结构与算法课程提供辅导，帮助理解复杂算法和解题技巧。',
    price: 60,
    unit: '小时',
    category: 'tutoring',
    tags: ['数据结构', '算法', '编程'],
    rating: 4.7,
    reviewCount: 18,
    provider: {
      id: 104,
      name: '赵强',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      department: '计算机科学学院',
      creditScore: 93
    }
  },
  {
    id: 5,
    title: '吉他弹唱教学',
    description: '从零基础开始学习吉他弹唱，包括基本乐理、和弦指法、节奏型和弹唱技巧。',
    price: 45,
    unit: '小时',
    category: 'music',
    tags: ['吉他', '弹唱', '音乐'],
    rating: 4.8,
    reviewCount: 27,
    provider: {
      id: 105,
      name: '陈杰',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      department: '音乐学院',
      creditScore: 94
    }
  },
  {
    id: 6,
    title: '毕业照写真拍摄',
    description: '提供校园内外景毕业照拍摄服务，包含简单后期修图，留下美好回忆。',
    price: 200,
    unit: '次',
    category: 'photography',
    tags: ['摄影', '毕业照', '写真'],
    rating: 4.5,
    reviewCount: 12,
    provider: {
      id: 106,
      name: '林小雨',
      avatar: 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg',
      department: '新闻传播学院',
      creditScore: 90
    }
  }
]);

// 筛选后的技能列表
const filteredSkillList = computed(() => {
  let result = [...skillList];
  
  // 搜索关键词筛选
  if (searchValue.value) {
    const keyword = searchValue.value.toLowerCase();
    result = result.filter(
      item => 
        item.title.toLowerCase().includes(keyword) || 
        item.description.toLowerCase().includes(keyword) ||
        item.tags.some(tag => tag.toLowerCase().includes(keyword))
    );
  }
  
  // 分类筛选
  if (selectedCategories.value.length > 0) {
    result = result.filter(item => selectedCategories.value.includes(item.category));
  }
  
  // 价格范围筛选
  result = result.filter(item => item.price >= priceRange.value[0] && item.price <= priceRange.value[1]);
  
  return result;
});

// 跳转到技能详情页
function goToSkillDetail(id: number) {
  router.push(`/skill-detail?id=${id}`);
}

// 跳转到发布技能页面
function goToPublishSkill() {
  router.push('/skill-publish');
}

// 清空筛选条件
function clearFilters() {
  searchValue.value = '';
  selectedCategories.value = [];
  priceRange.value = [0, 1000];
  showSearchPanel.value = false;
}

// 获取分类名称
function getCategoryName(categoryValue: string) {
  const category = categoryOptions.find(item => item.value === categoryValue);
  return category ? category.label : '';
}
</script>

<template>
  <div class="p-24px">
    <NCard :bordered="false" size="large" class="rounded-16px shadow-sm mb-16px">
      <div class="flex justify-between items-center">
        <div class="flex-1 max-w-800px">
          <NInput
            v-model:value="searchValue"
            placeholder="搜索技能、服务或技能标签"
            clearable
            size="large"
          >
            <template #prefix>
              <NIcon class="text-gray-400">
                <div class="i-material-symbols:search"></div>
              </NIcon>
            </template>
            <template #suffix>
              <NButton text @click="showSearchPanel = !showSearchPanel">
                <NIcon size="20">
                  <div class="i-material-symbols:filter-alt"></div>
                </NIcon>
              </NButton>
            </template>
          </NInput>
        </div>
        <div class="ml-16px">
          <NButton type="primary" size="large" @click="goToPublishSkill">
            <template #icon>
              <NIcon>
                <div class="i-material-symbols:add"></div>
              </NIcon>
            </template>
            发布我的技能
          </NButton>
        </div>
      </div>
      
      <!-- 高级搜索面板 -->
      <NCollapse v-if="showSearchPanel">
        <NCollapseItem title="高级筛选" name="1">
          <div class="py-16px">
            <div class="mb-16px">
              <div class="mb-8px font-medium">服务分类</div>
              <NSpace>
                <NCheckboxGroup v-model:value="selectedCategories">
                  <NSpace>
                    <NCheckbox
                      v-for="item in categoryOptions"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    />
                  </NSpace>
                </NCheckboxGroup>
              </NSpace>
            </div>
            
            <div class="mb-16px">
              <div class="mb-8px font-medium">价格区间</div>
              <NSlider
                v-model:value="priceRange"
                range
                :min="0"
                :max="1000"
                :step="10"
                style="max-width: 500px"
              />
              <div class="mt-4px text-14px text-gray-500">
                {{ priceRange[0] }} 学币 - {{ priceRange[1] }} 学币
              </div>
            </div>
            
            <div class="flex justify-end">
              <NButton @click="clearFilters" class="mr-16px">清空筛选</NButton>
              <NButton type="primary" @click="showSearchPanel = false">确定</NButton>
            </div>
          </div>
        </NCollapseItem>
      </NCollapse>
    </NCard>
    
    <!-- 技能列表 -->
    <div v-if="filteredSkillList.length > 0">
      <NGrid :cols="24" :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
        <NGi
          v-for="skill in filteredSkillList"
          :key="skill.id"
          span="24 s:12 m:8 l:6"
        >
          <NCard
            :bordered="false"
            size="small"
            class="rounded-16px shadow-sm cursor-pointer hover:shadow-md transition-all duration-300"
            hoverable
            @click="goToSkillDetail(skill.id)"
          >
            <template #cover>
              <div class="h-160px bg-gray-200 rounded-t-16px flex-center">
                <img
                  :src="`https://picsum.photos/400/250?random=${skill.id}`"
                  class="w-full h-full object-cover rounded-t-16px"
                  alt="技能封面"
                />
              </div>
            </template>
            
            <div class="pt-8px">
              <div class="flex-y-center">
                <NTag :bordered="false" round size="small" :type="skill.category === 'programming' ? 'success' : skill.category === 'design' ? 'info' : skill.category === 'translation' ? 'warning' : 'default'">
                  {{ getCategoryName(skill.category) }}
                </NTag>
                <div class="flex-1"></div>
                <div class="flex-y-center">
                  <div class="i-material-symbols:star text-yellow-500"></div>
                  <span class="ml-4px text-14px">{{ skill.rating }}</span>
                  <span class="ml-4px text-12px text-gray-400">({{ skill.reviewCount }})</span>
                </div>
              </div>
              
              <div class="font-medium text-16px line-clamp-1 mt-8px">{{ skill.title }}</div>
              
              <div class="text-gray-500 text-14px line-clamp-2 mt-4px h-40px">{{ skill.description }}</div>
              
              <div class="flex-y-center mt-8px">
                <NAvatar
                  :src="skill.provider.avatar"
                  round
                  size="small"
                  :style="{ backgroundColor: themeStore.themeColor }"
                />
                <span class="ml-8px text-14px">{{ skill.provider.name }}</span>
                <div class="flex-1"></div>
                <div class="text-16px font-medium text-primary">{{ skill.price }} <span class="text-12px text-gray-500">学币/{{ skill.unit }}</span></div>
              </div>
            </div>
          </NCard>
        </NGi>
      </NGrid>
    </div>
    
    <!-- 空状态 -->
    <NResult
      v-else
      status="404"
      title="暂无数据"
      description="没有找到符合条件的技能服务，请尝试其他搜索条件"
    >
      <template #footer>
        <NButton @click="clearFilters">清空筛选条件</NButton>
      </template>
    </NResult>
  </div>
</template>

<style scoped>
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style> 