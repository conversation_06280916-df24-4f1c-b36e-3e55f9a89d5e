<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchSkillCategories, fetchSkillTags, publishSkill } from '@/service/api/skill';
import { uploadFile, getFileUrl } from '@/service/api/file';
import { FilePurpose } from '@/enums/filePurpose';

defineOptions({
  name: 'SkillPublish'
});

const router = useRouter();
const { formRef, validate } = useNaiveForm();
const submitting = ref(false);
const coverUrl = ref('');
const uploadCoverLoading = ref(false);

// 表单数据
const formData = reactive({
  title: '',
  categoryId: null as number | null,
  tags: [],
  price: 0,
  duration: 120, // 服务时长（分钟）
  serviceType: 1, // 1-线上，2-线下
  location: '', // 服务地点
  maxStudents: 1, // 最大学员数
  description: '',
  content: '',
  serviceMethod: JSON.stringify(['online']), // 服务方式
  serviceTime: JSON.stringify({
    workday: true,
    workdayTime: ['18:00', '22:00'],
    weekend: true,
    weekendTime: ['09:00', '22:00']
  }),
  faqs: JSON.stringify([{
    question: '',
    answer: ''
  }]),
  images: [] as string[]
});

// 分类选项和标签选项（从后端获取）
const categoryOptions = ref<{ label: string; value: number }[]>([]);
const tagOptions = ref<{ label: string; value: string }[]>([]);

// 服务类型选项
const serviceTypeOptions = [
  { label: '线上服务', value: 1 },
  { label: '线下服务', value: 2 }
];

// 时长选项
const durationOptions = [
  { label: '30分钟', value: 30 },
  { label: '1小时', value: 60 },
  { label: '1.5小时', value: 90 },
  { label: '2小时', value: 120 },
  { label: '3小时', value: 180 },
  { label: '4小时', value: 240 }
];

// 上传封面图
async function handleUploadCover(options: any) {
  uploadCoverLoading.value = true;
  const { file } = options;

  try {
    // 调用文件上传API
    const fileUrl = await uploadFile(file.file as File, FilePurpose.SKILL_COVER);

    if (fileUrl) {
      // 存储原始路径用于提交表单
      const originalPath = fileUrl;
      // 生成完整的访问URL用于显示
      const fullUrl = getFileUrl(fileUrl);

      coverUrl.value = fullUrl;
      // 将原始路径添加到表单数据中（用于提交到后端）
      if (!formData.images.includes(originalPath)) {
        formData.images.unshift(originalPath); // 封面图放在第一位
      }
      window.$message?.success('封面图上传成功');
    } else {
      window.$message?.error('上传失败：未获取到文件URL');
    }
  } catch (error: any) {
    console.error('封面图上传失败:', error);
    window.$message?.error(error.message || '封面图上传失败');
  } finally {
    uploadCoverLoading.value = false;
  }
}

// FAQ数据（用于界面显示）
const faqList = ref([{
  question: '',
  answer: ''
}]);

// 服务图片上传
const uploadImageLoading = ref(false);

// 上传服务图片
async function handleUploadImage(options: any) {
  uploadImageLoading.value = true;
  const { file } = options;

  try {
    // 调用文件上传API
    const fileUrl = await uploadFile(file.file as File, FilePurpose.SKILL_IMAGE);

    if (fileUrl) {
      // 存储原始路径用于提交表单
      formData.images.push(fileUrl);
      window.$message?.success('图片上传成功');
    } else {
      window.$message?.error('上传失败：未获取到文件URL');
    }
  } catch (error: any) {
    console.error('图片上传失败:', error);
    window.$message?.error(error.message || '图片上传失败');
  } finally {
    uploadImageLoading.value = false;
  }
}

// 删除封面图
function removeCoverImage() {
  if (coverUrl.value) {
    // 从images数组中移除封面图
    const index = formData.images.indexOf(coverUrl.value);
    if (index > -1) {
      formData.images.splice(index, 1);
    }
    coverUrl.value = '';
  }
}

// 删除服务图片
function removeImage(index: number) {
  const imageUrl = formData.images[index];
  // 如果删除的是封面图，也要清空封面图
  if (imageUrl === coverUrl.value) {
    coverUrl.value = '';
  }
  formData.images.splice(index, 1);
}

// 添加FAQ
function addFaq() {
  faqList.value.push({
    question: '',
    answer: ''
  });
}

// 移除FAQ
function removeFaq(index: number) {
  if (faqList.value.length > 1) {
    faqList.value.splice(index, 1);
  } else {
    window.$message?.warning('至少保留一个FAQ');
  }
}

// 初始化数据
onMounted(async () => {
  await loadCategories();
  await loadTags();
});

// 加载分类数据
async function loadCategories() {
  try {
    const response = await fetchSkillCategories();

    // 处理不同的响应格式
    if (Array.isArray(response)) {
      categoryOptions.value = response.map((item: any) => ({
        label: item.name,
        value: item.categoryId
      }));
    } else if (response && response.data && Array.isArray(response.data)) {
      categoryOptions.value = response.data.map((item: any) => ({
        label: item.name,
        value: item.categoryId
      }));
    } else if (response && typeof response === 'object') {
      for (const [key, value] of Object.entries(response)) {
        if (Array.isArray(value)) {
          categoryOptions.value = value.map((item: any) => ({
            label: item.name,
            value: item.categoryId
          }));
          break;
        }
      }
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    window.$message?.error('加载分类失败');
  }
}

// 加载标签数据
async function loadTags() {
  try {
    const response = await fetchSkillTags();

    // 处理不同的响应格式
    if (Array.isArray(response)) {
      tagOptions.value = response.map((tag: string) => ({
        label: tag,
        value: tag
      }));
    } else if (response && response.data && Array.isArray(response.data)) {
      tagOptions.value = response.data.map((tag: string) => ({
        label: tag,
        value: tag
      }));
    } else if (response && typeof response === 'object') {
      for (const [key, value] of Object.entries(response)) {
        if (Array.isArray(value)) {
          tagOptions.value = value.map((tag: string) => ({
            label: tag,
            value: tag
          }));
          break;
        }
      }
    }
  } catch (error) {
    console.error('加载标签失败:', error);
    window.$message?.error('加载标签失败');
  }
}

// 提交表单
async function handleSubmit() {
  try {
    await validate();
    submitting.value = true;

    // 准备提交数据
    const submitData = {
      title: formData.title,
      categoryId: formData.categoryId,
      tags: formData.tags,
      price: formData.price,
      duration: formData.duration,
      serviceType: formData.serviceType,
      location: formData.location,
      maxStudents: formData.maxStudents,
      description: formData.description,
      content: formData.content,
      serviceMethod: formData.serviceMethod,
      serviceTime: formData.serviceTime,
      faqs: JSON.stringify(faqList.value),
      images: formData.images
    };

    const response = await publishSkill(submitData);
    console.log('发布响应:', response);
    console.log('响应类型:', typeof response);
    console.log('serviceId:', response?.serviceId);

    // 由于使用了 transformBackendResponse，成功的响应会直接返回 data 部分
    // 如果请求成功，response 就是 { serviceId: 1 } 这样的数据
    if (response && response.serviceId) {
      window.$message?.success('技能发布成功！');
      router.push('/skill');
    } else {
      console.warn('发布失败，响应数据:', response);
      window.$message?.error('发布失败');
    }
  } catch (error: any) {
    console.error('发布失败:', error);
    window.$message?.error(error.message || '发布失败');
  } finally {
    submitting.value = false;
  }
}

// 预览
function previewSkill() {
  window.$message?.info('预览功能开发中');
}

// 返回
function goBack() {
  router.push('/skill');
}
</script>

<template>
  <div class="p-24px">
    <div class="flex items-center mb-16px">
      <NButton quaternary circle @click="goBack">
        <div class="i-material-symbols:arrow-back text-18px"></div>
      </NButton>
      <span class="ml-8px">返回技能列表</span>
    </div>
    
    <NCard title="发布技能" :bordered="false" size="large" class="rounded-16px shadow-sm">
      <NForm
        ref="formRef"
        :model="formData"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <!-- 基本信息 -->
        <div class="pb-16px mb-24px border-b border-gray-200">
          <h2 class="text-18px font-bold mb-16px">基本信息</h2>
          
          <NFormItem label="技能标题" path="title" :rule="{ required: true, message: '请输入技能标题' }">
            <NInput v-model:value="formData.title" placeholder="请输入能吸引人的技能标题，简洁明了" />
          </NFormItem>
          
          <NFormItem label="封面图" path="coverUrl">
            <div class="flex items-start">
              <NUpload
                action="#"
                :max="1"
                :show-file-list="false"
                :custom-request="handleUploadCover"
                accept="image/jpeg,image/jpg,image/png,image/webp"
              >
                <NButton :loading="uploadCoverLoading">
                  <template #icon>
                    <div class="i-material-symbols:upload"></div>
                  </template>
                  上传封面图
                </NButton>
              </NUpload>

              <div v-if="coverUrl" class="ml-16px">
                <div class="h-100px w-180px relative overflow-hidden rounded-8px border border-gray-200">
                  <img :src="coverUrl" class="w-full h-full object-cover" alt="封面预览" />
                  <div class="absolute top-2 right-2">
                    <NButton
                      circle
                      size="small"
                      quaternary
                      type="error"
                      @click="removeCoverImage"
                    >
                      <template #icon>
                        <div class="i-material-symbols:close text-12px"></div>
                      </template>
                    </NButton>
                  </div>
                </div>
                <div class="mt-4px text-gray-400 text-12px">推荐尺寸: 800x400px，支持 JPG、PNG、WebP 格式</div>
              </div>
            </div>
          </NFormItem>
          
          <NFormItem label="技能分类" path="categoryId" :rule="{ required: true, message: '请选择技能分类' }">
            <NSelect
              v-model:value="formData.categoryId"
              :options="categoryOptions"
              placeholder="请选择技能分类"
            />
          </NFormItem>
          
          <NFormItem label="技能标签" path="tags" :rule="{ required: true, message: '请选择或添加技能标签' }">
            <NSelect
              v-model:value="formData.tags"
              :options="tagOptions"
              placeholder="请选择或添加技能标签"
              multiple
              filterable
              tag
            />
          </NFormItem>
          
          <NFormItem label="服务单价" path="price" :rule="{ required: true, type: 'number', min: 1, message: '请输入大于0的服务单价' }">
            <NInputNumber
              v-model:value="formData.price"
              :min="1"
              placeholder="请输入服务单价"
              style="width: 100%"
            >
              <template #prefix>学币</template>
              <template #suffix>/次</template>
            </NInputNumber>
          </NFormItem>

          <NFormItem label="服务时长" path="duration" :rule="{ required: true, message: '请选择服务时长' }">
            <NSelect
              v-model:value="formData.duration"
              :options="durationOptions"
              placeholder="请选择服务时长"
            />
          </NFormItem>

          <NFormItem label="服务类型" path="serviceType" :rule="{ required: true, message: '请选择服务类型' }">
            <NRadioGroup v-model:value="formData.serviceType">
              <NRadio v-for="item in serviceTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadio>
            </NRadioGroup>
          </NFormItem>

          <NFormItem
            v-if="formData.serviceType === 2"
            label="服务地点"
            path="location"
            :rule="{ required: true, message: '线下服务必须填写服务地点' }"
          >
            <NInput
              v-model:value="formData.location"
              placeholder="请输入服务地点，如：理工校区图书馆"
            />
          </NFormItem>

          <NFormItem label="最大学员数" path="maxStudents" :rule="{ required: true, type: 'number', min: 1, max: 10, message: '学员数必须在1-10之间' }">
            <NInputNumber
              v-model:value="formData.maxStudents"
              :min="1"
              :max="10"
              placeholder="最大学员数"
              style="width: 100%"
            />
          </NFormItem>
          
          <NFormItem label="简介" path="description" :rule="{ required: true, message: '请输入技能简介' }">
            <NInput
              v-model:value="formData.description"
              type="textarea"
              placeholder="请简要介绍你的技能服务，不超过100字"
              :maxlength="100"
              show-count
              :autosize="{ minRows: 2, maxRows: 3 }"
            />
          </NFormItem>
        </div>
        
        <!-- 服务详情 -->
        <div class="pb-16px mb-24px border-b border-gray-200">
          <h2 class="text-18px font-bold mb-16px">服务详情</h2>
          
          <NFormItem label="详细内容" path="content" :rule="{ required: true, message: '请输入详细的服务内容介绍' }">
            <NInput
              v-model:value="formData.content"
              type="textarea"
              placeholder="请详细描述你的服务内容，包括服务流程、你的专业背景和特色等"
              :autosize="{ minRows: 6, maxRows: 10 }"
            />
          </NFormItem>

          <NFormItem label="服务图片" path="images">
            <div class="w-full">
              <div class="mb-16px">
                <NUpload
                  action="#"
                  :max="5"
                  :show-file-list="false"
                  :custom-request="handleUploadImage"
                  accept="image/jpeg,image/jpg,image/png,image/webp"
                  multiple
                >
                  <NButton :loading="uploadImageLoading" :disabled="formData.images.length >= 5">
                    <template #icon>
                      <div class="i-material-symbols:add-photo-alternate"></div>
                    </template>
                    添加服务图片 ({{ formData.images.length }}/5)
                  </NButton>
                </NUpload>
                <div class="mt-4px text-gray-400 text-12px">
                  最多上传5张图片，支持 JPG、PNG、WebP 格式，建议尺寸 800x600px
                </div>
              </div>

              <!-- 图片预览 -->
              <div v-if="formData.images.length > 0" class="grid grid-cols-3 gap-12px">
                <div
                  v-for="(image, index) in formData.images"
                  :key="index"
                  class="relative group"
                >
                  <div class="h-120px w-full relative overflow-hidden rounded-8px border border-gray-200">
                    <img :src="getFileUrl(image)" class="w-full h-full object-cover" :alt="`服务图片${index + 1}`" />
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <NButton
                        circle
                        size="small"
                        quaternary
                        type="error"
                        class="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        @click="removeImage(index)"
                      >
                        <template #icon>
                          <div class="i-material-symbols:delete-outline text-16px text-white"></div>
                        </template>
                      </NButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </NFormItem>

        </div>
        
        <!-- 常见问题 -->
        <div>
          <div class="flex justify-between items-center mb-16px">
            <h2 class="text-18px font-bold">常见问题</h2>
            <NButton @click="addFaq" size="small">
              <template #icon>
                <div class="i-material-symbols:add"></div>
              </template>
              添加问题
            </NButton>
          </div>
          
          <div v-for="(faq, index) in faqList" :key="index" class="mb-16px p-16px border border-gray-200 rounded-8px">
            <div class="flex justify-between items-center mb-8px">
              <div class="font-medium">问题 {{ index + 1 }}</div>
              <NButton circle quaternary size="small" @click="removeFaq(index)">
                <template #icon>
                  <div class="i-material-symbols:delete-outline"></div>
                </template>
              </NButton>
            </div>
            
            <NInput
              v-model:value="faq.question"
              placeholder="请输入常见问题"
              class="mb-8px"
            />
            
            <NInput
              v-model:value="faq.answer"
              type="textarea"
              placeholder="请输入问题答案"
              :autosize="{ minRows: 2, maxRows: 3 }"
            />
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex justify-center mt-32px gap-16px">
          <NButton @click="goBack">取消</NButton>
          <NButton type="info" @click="previewSkill">预览</NButton>
          <NButton type="primary" :loading="submitting" @click="handleSubmit">发布技能</NButton>
        </div>
      </NForm>
    </NCard>
  </div>
</template>

<style scoped></style> 