<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { $t } from '@/locales';
import { loginModuleRecord } from '@/constants/app';
import { useRouterPush } from '@/hooks/common/router';
import { useNaiveForm } from '@/hooks/common/form';
import { useAuthStore } from '@/store/modules/auth';
import { useRouter } from 'vue-router';
import { useCaptcha } from '@/hooks/business/captcha';

defineOptions({
  name: 'PwdLogin'
});

const router = useRouter();
const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();

// 登录方式切换
const loginType = ref<'password' | 'code'>('password');

// 密码登录表单
interface PasswordFormModel {
  email: string;
  password: string;
}

const passwordModel: PasswordFormModel = reactive({
  email: '',
  password: ''
});

// 验证码登录表单
interface CodeFormModel {
  email: string;
  code: string;
}

const codeModel: CodeFormModel = reactive({
  email: '',
  code: ''
});

const passwordRules = computed(() => {
  return {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email' as const, message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' }
    ]
  };
});

const codeRules = computed(() => {
  return {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email' as const, message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    code: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
    ]
  };
});

// 密码登录提交
async function handlePasswordSubmit() {
  try {
    await validate();
    console.log('提交登录请求:', { email: passwordModel.email, password: '***' });
    const result = await authStore.login(passwordModel.email, passwordModel.password);
    console.log("密码登录结果:", result);
    
    if (!result) {
      console.log('登录结果为空，可能登录失败');
      return;
    }
    
    window.$message?.success($t('page.login.common.validateSuccess'));
  } catch (error: any) {
    console.error('登录失败:', error);
    window.$message?.error(error.message || '登录失败');
  }
}

// 验证码登录提交
async function handleCodeSubmit() {
  try {
    await validate();
    await authStore.loginWithCode(codeModel.email, codeModel.code);
  } catch (error: any) {
    console.error('登录失败:', error);
    window.$message?.error(error.message || '登录失败');
  }
}

// 当前登录方式对应的模型和规则
const currentModel = computed(() => {
  return loginType.value === 'password' ? passwordModel : codeModel;
});

const currentRules = computed(() => {
  return loginType.value === 'password' ? passwordRules.value : codeRules.value;
});

type AccountKey = 'super' | 'admin' | 'user';

interface Account {
  key: AccountKey;
  label: string;
  userName: string;
  password: string;
}

const accounts = computed<Account[]>(() => [
  // {
  //   key: 'super',
  //   label: $t('page.login.pwdLogin.superAdmin'),
  //   userName: '<EMAIL>',
  //   password: '2b52c119-148'
  // },
  // {
  //   key: 'user',
  //   label: $t('page.login.pwdLogin.user'),
  //   userName: '<EMAIL>',
  //   password: '2b52c119-148'
  // }
]);

async function handleAccountLogin(account: Account) {
  await authStore.login(account.userName, account.password);
}
</script>

<template>
  <div>
    <!-- 登录方式切换 -->
    <div class="mb-24px">
      <NTabs v-model:value="loginType" type="segment">
        <NTabPane name="password" tab="密码登录" />
        <NTabPane name="code" tab="验证码登录" />
      </NTabs>
    </div>

    <NForm ref="formRef" :model="currentModel" :rules="currentRules" size="large" :show-label="false">
      <!-- 密码登录表单 -->
      <template v-if="loginType === 'password'">
        <NFormItem path="email">
          <NInput v-model:value="passwordModel.email" placeholder="请输入邮箱地址">
            <template #prefix>
              <SvgIcon icon="carbon:email" class="text-18px text-primary" />
            </template>
          </NInput>
        </NFormItem>
        <NFormItem path="password">
          <NInput v-model:value="passwordModel.password" type="password" show-password-on="click" placeholder="请输入密码">
            <template #prefix>
              <SvgIcon icon="carbon:locked" class="text-18px text-primary" />
            </template>
          </NInput>
        </NFormItem>
      </template>

      <!-- 验证码登录表单 -->
      <template v-else>
        <NFormItem path="email">
          <NInput v-model:value="codeModel.email" placeholder="请输入邮箱地址">
            <template #prefix>
              <SvgIcon icon="carbon:email" class="text-18px text-primary" />
            </template>
          </NInput>
        </NFormItem>
        <NFormItem path="code">
          <div class="w-full flex-y-center gap-16px">
            <NInput v-model:value="codeModel.code" placeholder="请输入验证码">
              <template #prefix>
                <SvgIcon icon="carbon:checkmark-outline" class="text-18px text-primary" />
              </template>
            </NInput>
            <NButton size="large" :disabled="isCounting || !codeModel.email" :loading="loading" @click="getCaptcha(codeModel.email)">
              {{ label }}
            </NButton>
          </div>
        </NFormItem>
      </template>

      <NSpace vertical :size="24">
        <div class="flex-y-center justify-between">
          <NCheckbox>{{ $t('page.login.pwdLogin.rememberMe') }}</NCheckbox>
          <NButton quaternary @click="toggleLoginModule('reset-pwd')">
            {{ $t('page.login.pwdLogin.forgetPassword') }}
          </NButton>
        </div>
        <NButton 
          type="primary" 
          size="large" 
          round 
          block 
          :loading="authStore.loginLoading" 
          @click="loginType === 'password' ? handlePasswordSubmit() : handleCodeSubmit()"
        >
          {{ $t('common.confirm') }}
        </NButton>
        <div class="flex-y-center justify-center gap-12px">
          <NButton class="flex-1" block @click="toggleLoginModule('register')">
            {{ $t(loginModuleRecord.register) }}
          </NButton>
        </div>
      </NSpace>
    </NForm>
  </div>
</template>

<style scoped>
.account-list {
  margin-top: 12px;
}

.account-item {
  margin-bottom: 6px;
}
</style>
