<script setup lang="ts">
import { computed, reactive } from 'vue';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { fetchRegister } from '@/service/api/user';

defineOptions({
  name: 'Register'
});

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();

interface FormModel {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  verificationCode: string;
}

const model: FormModel = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
});

const rules = computed(() => {
  return {
    name: [
      { required: true, message: '请输入姓名', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email' as const, message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请确认密码', trigger: 'blur' },
      {
        validator: (_rule: any, value: string) => {
          if (value !== model.password) {
            return new Error('两次输入的密码不一致');
          }
          return true;
        },
        trigger: 'blur'
      }
    ],
    verificationCode: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
    ]
  };
});

async function handleSubmit() {
  try {
    await validate();
    
    // 调用注册API
    await fetchRegister({
      name: model.name,
      email: model.email,
      password: model.password,
      confirmPassword: model.confirmPassword,
      verificationCode: model.verificationCode
    });

    window.$message?.success('注册成功！请登录');
    // 注册成功后跳转到登录页
    toggleLoginModule('pwd-login');
  } catch (error: any) {
    console.error('注册失败:', error);
    window.$message?.error(error.message || '注册失败');
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false">
    <NFormItem path="name">
      <NInput v-model:value="model.name" placeholder="请输入姓名">
        <template #prefix>
          <SvgIcon icon="carbon:user" class="text-18px text-primary" />
        </template>
      </NInput>
    </NFormItem>
    <NFormItem path="email">
      <NInput v-model:value="model.email" placeholder="请输入邮箱地址">
        <template #prefix>
          <SvgIcon icon="carbon:email" class="text-18px text-primary" />
        </template>
      </NInput>
    </NFormItem>
    <NFormItem path="verificationCode">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.verificationCode" placeholder="请输入验证码">
          <template #prefix>
            <SvgIcon icon="carbon:checkmark-outline" class="text-18px text-primary" />
          </template>
        </NInput>
        <NButton size="large" :disabled="isCounting || !model.email" :loading="loading" @click="getCaptcha(model.email)">
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NFormItem path="password">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
        placeholder="请输入密码"
      >
        <template #prefix>
          <SvgIcon icon="carbon:locked" class="text-18px text-primary" />
        </template>
      </NInput>
    </NFormItem>
    <NFormItem path="confirmPassword">
      <NInput
        v-model:value="model.confirmPassword"
        type="password"
        show-password-on="click"
        placeholder="请确认密码"
      >
        <template #prefix>
          <SvgIcon icon="carbon:locked" class="text-18px text-primary" />
        </template>
      </NInput>
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
