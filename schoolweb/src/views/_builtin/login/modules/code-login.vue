<script setup lang="ts">
import { computed, reactive } from 'vue';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { useAuthStore } from '@/store/modules/auth';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'CodeLogin'
});

const router = useRouter();
const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();

interface FormModel {
  email: string;
  code: string;
}

const model: FormModel = reactive({
  email: '',
  code: ''
});

const rules = computed(() => ({
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email' as const, message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ]
}));

async function handleSubmit() {
  try {
    await validate();
    
    // 调用验证码登录API
    // @ts-ignore - 忽略类型检查
    const result = await authStore.loginWithCode(model.email, model.code);
    console.log("验证码登录结果:", result);
    
    if (!result) {
      return;
    }
    
    window.$message?.success($t('page.login.common.validateSuccess'));
  } catch (error: any) {
    console.error('登录失败:', error);
    window.$message?.error(error.message || '登录失败');
  }
}
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false">
    <NFormItem path="email">
      <NInput v-model:value="model.email" placeholder="请输入邮箱地址">
        <template #prefix>
          <SvgIcon icon="carbon:email" class="text-18px text-primary" />
        </template>
      </NInput>
    </NFormItem>
    <NFormItem path="code">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.code" placeholder="请输入验证码" />
        <NButton size="large" :disabled="isCounting || !model.email" :loading="loading" @click="getCaptcha(model.email)">
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block :loading="authStore.loginLoading" @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<style scoped></style>
