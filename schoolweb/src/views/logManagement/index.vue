<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMessage } from 'naive-ui';
import { useAuthStore } from '@/store/modules/auth';
import { ElDatePicker } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import type { TableColumnCtx } from 'element-plus';
import {
  fetchSystemLogs,
  fetchOperationTypes,
  fetchOperationModules,
  fetchResponseCodes,
  type SystemLog
} from '@/service/api/log';

const authStore = useAuthStore();
const isAdmin = computed(() => authStore.isAdmin);
const message = useMessage();

// 表格数据
const tableData = ref<SystemLog[]>([]);
const total = ref(0);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(25);
// 时间选择
const defaultTime2: [Date, Date] = [
  new Date(2000, 1, 1),
  new Date(2000, 2, 1)
]
// 将第一个日期的时间设置为 00:00:00
defaultTime2[0].setHours(0, 0, 0, 0);
// 将第二个日期的时间设置为 23:59:59
defaultTime2[1].setHours(23, 59, 59, 999);
const dateRange = computed({
  get() {
    return [searchForm.value.startTime, searchForm.value.endTime];
  },
  set(newRange) {
    if (newRange && newRange.length === 2) {
      searchForm.value.startTime = newRange[0];
      searchForm.value.endTime = newRange[1];
    }
  }
});
// 查询条件
const searchForm = ref({
  startTime: new Date(new Date().setHours(0, 0, 0, 0)).toISOString().slice(0, 19).replace('T', ' '),
  endTime: new Date(new Date().setHours(23, 59, 59, 999)).toISOString().slice(0, 19).replace('T', ' '),
  userId: '',
  username: '',
  operationType: '',
  operationModule: '',
  responseCode: ''
});

// 操作类型选项
const operationTypeOptions = ref([
  { label: '全部', value: '' }
]);

// 模块选项
const moduleOptions = ref([
  { label: '全部', value: '' }
]);

// 响应状态选项
const responseCodeOptions = ref([
  { label: '全部', value: '' }
]);

// 加载选项数据
const loadOptions = async () => {
  try {
    // 加载操作类型
    const types = await fetchOperationTypes();
    operationTypeOptions.value = [
      { label: '全部', value: '' },
      ...types.filter(type => type !== null).map(type => ({
        label: type,
        value: type
      }))
    ];

    // 加载操作模块
    const modules = await fetchOperationModules();
    moduleOptions.value = [
      { label: '全部', value: '' },
      ...modules.filter(module => module !== null).map(module => ({
        label: module,
        value: module
      }))
    ];

    // 加载响应状态
    const codes = await fetchResponseCodes();
    responseCodeOptions.value = [
      { label: '全部', value: '' },
      ...codes.filter(code => code !== null).map(code => ({
        label: code?.toString(),
        value: code?.toString()
      }))
    ];
  } catch (error) {
    message.error('加载选项数据失败');
    console.error('加载选项数据错误:', error);
  }
};

// 加载日志列表
const loadLogs = async () => {
  if (!isAdmin.value) {
    message.error('只有管理员可以访问此页面');
    return;
  }

  loading.value = true;
  try {
    const response = await fetchSystemLogs({
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm.value
    });

    if (response) {
      tableData.value = response.records;
      total.value = response.total;
      currentPage.value = response.current;
    } else {
      tableData.value = [];
      total.value = 0;
      currentPage.value = 1;
    }
  } catch (error) {
    message.error('获取日志列表失败');
    console.error('获取日志列表错误:', error);
  } finally {
    loading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  currentPage.value = page;
  loadLogs();
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadLogs();
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  loadLogs();
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.value = {
    startTime: new Date(new Date().setHours(0, 0, 0, 0)).toISOString().slice(0, 19).replace('T', ' '),
    endTime: new Date(new Date().setHours(23, 59, 59, 999)).toISOString().slice(0, 19).replace('T', ' '),
    userId: '',
    username: '',
    operationType: '',
    operationModule: '',
    responseCode: ''
  };
  handleSearch();
};

// 格式化日期
const formatDateTime = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleString();
};

// 格式化执行时长
const formatExecutionTime = (time: number) => {
  if (!time) return '-';
  return `${time}ms`;
};

// 获取响应状态样式
const getResponseCodeType = (code: number) => {
  if (code === 200) return 'success';
  return 'danger';
};

onMounted(() => {
  if (isAdmin.value) {
    loadOptions();
    loadLogs();
  }
});
</script>

<template>
  <div v-if="isAdmin" class="log-management-page">
    <div class="page-header">
      <h2>系统日志</h2>
    </div>

    <!-- 搜索表单 -->
    <el-form :model="searchForm" label-width="auto" :inline="true" class="search-form-container">
      <el-form-item label="操作时间">
        <el-date-picker type="datetimerange" start-placeholder="开始时间" range-separator="至" end-placeholder="结束时间"
          :default-time="defaultTime2" value-format="YYYY-MM-DD HH:mm:ss" v-model="dateRange" clearable />
        <!-- <el-date-picker v-model="searchForm.startTime" type="datetime" placeholder="开始时间"
          value-format="YYYY-MM-DD HH:mm:ss" class="date-picker" :clearable="false" :editable="false" />
        <span class="separator">至</span>
        <el-date-picker v-model="searchForm.endTime" type="datetime" placeholder="结束时间"
          value-format="YYYY-MM-DD HH:mm:ss" class="date-picker" :clearable="false" :editable="false" /> -->
      </el-form-item>
      <el-form-item label="用户ID">
        <el-input v-model="searchForm.userId" placeholder="请输入用户ID" clearable />
      </el-form-item>
      <el-form-item label="用户名">
        <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
      </el-form-item>
      <el-form-item label="操作类型">
        <el-select v-model="searchForm.operationType" placeholder="请选择操作类型" clearable class="full-width-select"
          style="width: 180px;">
          <el-option v-for="item in operationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="操作模块">
        <el-select v-model="searchForm.operationModule" placeholder="请选择操作模块" clearable class="full-width-select"
          style="width: 180px;">
          <el-option v-for="item in moduleOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="响应状态">
        <el-select v-model="searchForm.responseCode" placeholder="请选择响应状态" clearable class="full-width-select"
          style="width: 180px;">
          <el-option v-for="item in responseCodeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="ml-[20px]">
        <el-button type="primary" :icon="Search" @click="handleSearch">查询</el-button>
        <el-button :icon="Refresh" @click="resetSearch">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 日志表格 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }" :cell-style="{ padding: '8px 0' }"
      height="calc(100vh - 380px)">
      <el-table-column prop="operationTime" label="操作时间" width="160" align="center">
        <template #default="{ row }">
          {{ formatDateTime(row.operationTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="username" label="用户名" width="120" align="center" />
      <el-table-column prop="userId" label="用户ID" width="100" align="center" />
      <el-table-column prop="operationModule" label="操作模块" width="120" align="center" />
      <el-table-column prop="operationType" label="操作类型" width="100" align="center" />
      <el-table-column prop="operationDesc" label="操作描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="requestUrl" label="请求URL" min-width="200" show-overflow-tooltip />
      <el-table-column prop="requestMethod" label="请求方法" width="100" align="center" />
      <el-table-column prop="operationIp" label="操作IP" width="130" align="center" />
      <el-table-column prop="responseCode" label="响应状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getResponseCodeType(row.responseCode)" size="small">
            {{ row.responseCode }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="executionTime" label="执行时长" width="100" align="center">
        <template #default="{ row }">
          {{ formatExecutionTime(row.executionTime) }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
        :page-sizes="[10, 25, 50, 100]" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
        @current-change="handlePageChange" background />
    </div>
  </div>
  <div v-else class="unauthorized">
    <h2>未授权访问</h2>
    <p>只有管理员可以访问此页面</p>
  </div>
</template>

<style scoped>
.log-management-page {
  padding: 24px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  height: 100%;
}

.page-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.page-header h2 {
  font-size: 20px;
  color: #303133;
  margin: 0;
  font-weight: 500;
}

.search-form {
  margin-bottom: 24px;
  background: #f5f7fa;
  border-radius: 4px;
  padding: 24px;
}

.search-form-container {
  margin-top: 10px;
  margin-left: 30px;
  margin-bottom: 20px;
}

.search-form-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  align-items: flex-start;
}

.search-row:first-child {
  grid-template-columns: 1fr;
}

.date-range-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* .date-picker {
  flex: 1;
} */

.separator {
  color: #606266;
  padding: 0 8px;
}

.operation-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* .full-width-select {
  width: 100%;
} */

/* :deep(.el-form-item) {
  margin-bottom: 20px;
} */

/* 
:deep(.el-input__wrapper),
:deep(.el-select),
:deep(.el-date-editor.el-input) {
  width: 100%;
} */

:deep(.el-table) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table--border) {
  border: 1px solid #ebeef5;
}

:deep(.el-table td) {
  padding: 8px 0;
}

:deep(.el-tag) {
  width: 60px;
  text-align: center;
}

.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

.unauthorized {
  text-align: center;
  padding: 100px 0;
  color: #909399;
}

.unauthorized h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #606266;
}

@media (max-width: 1200px) {
  .search-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .log-management-page {
    padding: 16px;
  }

  .search-form {
    padding: 16px;
  }

  .search-row {
    grid-template-columns: 1fr;
  }

  .date-range-container {
    flex-direction: column;
  }

  .separator {
    display: none;
  }

  .operation-buttons {
    justify-content: center;
    margin-top: 8px;
  }
}
</style>