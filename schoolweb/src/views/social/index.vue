<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { fetchPostList, likePost, followUser, createPost } from '@/service/api/social';
import type { Post } from '@/service/api/social';
import { fetchUserInfo } from '@/service/api/user';
import { useThemeStore } from '@/store/modules/theme';

defineOptions({
  name: 'Social'
});

const router = useRouter();
const themeStore = useThemeStore();

// 当前用户信息
const currentUser = ref<{ userId: number; name: string; avatar: string } | null>(null);

// 动态列表
const postList = ref<Post[]>([]);
const loading = ref(false);
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 新建动态
const showCreatePostModal = ref(false);
const newPostForm = reactive({
  content: '',
  images: [] as string[],
  skillId: undefined as number | undefined
});
const uploadImages = ref<any[]>([]);

// 获取用户信息
async function getUserInfo() {
  try {
    const res = await fetchUserInfo();
    if (res && res.userId) {
      currentUser.value = {
        userId: res.userId,
        name: res.name,
        avatar: res.avatar
      };
    }
  } catch (error) {
    console.error('获取用户信息失败', error);
  }
}

// 获取动态列表
async function getPosts(isRefresh = false) {
  if (isRefresh) {
    pagination.page = 1;
  }
  loading.value = true;
  try {
    const res = await fetchPostList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      followed: activeTab.value === 'followed'
    });
    if (res && res.records) {
      if (isRefresh) {
        postList.value = res.records;
      } else {
        postList.value = [...postList.value, ...res.records];
      }
      pagination.total = res.total;
    }
  } catch (error) {
    console.error('获取动态列表失败', error);
  } finally {
    loading.value = false;
  }
}

// 加载更多
function loadMore() {
  if (postList.value.length < pagination.total) {
    pagination.page += 1;
    getPosts();
  }
}

// 点赞/取消点赞
async function handleLike(post: Post) {
  try {
    await likePost(post.id, !post.isLiked);
    post.isLiked = !post.isLiked;
    post.likeCount = post.isLiked ? post.likeCount + 1 : post.likeCount - 1;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 关注/取消关注
async function handleFollow(userId: number, index: number) {
  try {
    const isFollowed = postList.value[index].isFollowed;
    await followUser(userId, !isFollowed);
    postList.value[index].isFollowed = !isFollowed;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 发布动态
async function handleCreatePost() {
  try {
    await createPost({
      content: newPostForm.content,
      images: uploadImages.value.map(item => item.url),
      skillId: newPostForm.skillId
    });
    window.$message?.success('发布成功');
    showCreatePostModal.value = false;
    newPostForm.content = '';
    newPostForm.skillId = undefined;
    uploadImages.value = [];
    getPosts(true);
  } catch (error) {
    console.error('发布失败', error);
  }
}

// 查看动态详情
function viewPostDetail(postId: number) {
  router.push(`/social/post/${postId}`);
}

// 查看用户资料
function viewUserProfile(userId: number) {
  router.push(`/user/${userId}`);
}

// 切换标签
const activeTab = ref<'all' | 'followed'>('all');
function changeTab(tab: 'all' | 'followed') {
  if (activeTab.value !== tab) {
    activeTab.value = tab;
    getPosts(true);
  }
}

// 自定义上传
function customRequest({ file }: { file: File }) {
  const formData = new FormData();
  formData.append('file', file);
  
  return new Promise((resolve, reject) => {
    // 模拟上传
    setTimeout(() => {
      const url = URL.createObjectURL(file);
      uploadImages.value.push({ name: file.name, url });
      resolve(url);
    }, 1000);
  });
}

onMounted(() => {
  getUserInfo();
  getPosts();
});
</script>

<template>
  <div class="p-24px">
    <div class="flex gap-24px">
      <!-- 左侧 - 动态内容 -->
      <div class="flex-1">
        <!-- 发布动态卡片 -->
        <NCard :bordered="false" class="mb-16px shadow-sm">
          <div class="flex gap-16px">
            <NAvatar v-if="currentUser?.avatar" :src="currentUser.avatar" size="large" />
            <NAvatar v-else size="large">{{ currentUser?.name?.substring(0, 1) || '用' }}</NAvatar>
            <div class="flex-1">
              <NInput 
                placeholder="分享你的技能、经验或想法..." 
                type="textarea" 
                :rows="3" 
                @click="showCreatePostModal = true"
              />
              <div class="flex justify-end mt-16px">
                <NButton type="primary" @click="showCreatePostModal = true">
                  <template #icon>
                    <NIcon>
                      <div class="i-material-symbols:edit"></div>
                    </NIcon>
                  </template>
                  发布动态
                </NButton>
              </div>
            </div>
          </div>
        </NCard>
        
        <!-- 标签切换 -->
        <div class="mb-16px">
          <NTabs v-model:value="activeTab" type="segment">
            <NTabPane name="all" tab="全部动态" />
            <NTabPane name="followed" tab="关注的人" />
          </NTabs>
        </div>
        
        <!-- 动态列表 -->
        <div>
          <NScrollbar style="max-height: calc(100vh - 280px);">
            <NList>
              <NEmpty v-if="postList.length === 0" description="暂无动态" />
              <NListItem v-for="(post, index) in postList" :key="post.id">
                <NCard :bordered="false" class="mb-16px shadow-sm">
                  <!-- 用户信息 -->
                  <div class="flex justify-between items-center mb-16px">
                    <div class="flex items-center gap-12px cursor-pointer" @click="viewUserProfile(post.userId)">
                      <NAvatar v-if="post.userAvatar" :src="post.userAvatar" size="medium" />
                      <NAvatar v-else size="medium">{{ post.userName?.substring(0, 1) || '用' }}</NAvatar>
                      <div>
                        <div class="font-medium">{{ post.userName }}</div>
                        <NTime :time="new Date(post.createdTime)" format="yyyy-MM-dd HH:mm" />
                      </div>
                    </div>
                    <NButton v-if="post.userId !== currentUser?.userId" 
                      :type="post.isFollowed ? 'default' : 'primary'" 
                      size="small"
                      @click="handleFollow(post.userId, index)"
                    >
                      {{ post.isFollowed ? '已关注' : '关注' }}
                    </NButton>
                  </div>
                  
                  <!-- 动态内容 -->
                  <div class="mb-16px cursor-pointer" @click="viewPostDetail(post.id)">
                    <div class="mb-12px text-base whitespace-pre-wrap">{{ post.content }}</div>
                    
                    <!-- 图片 -->
                    <div v-if="post.images && post.images.length > 0" class="grid gap-8px image-grid">
                      <div v-for="(img, imgIndex) in post.images" :key="imgIndex" class="image-item">
                        <img :src="img" alt="动态图片" class="w-full h-full object-cover rounded-4px" />
                      </div>
                    </div>
                    
                    <!-- 关联技能 -->
                    <NTag v-if="post.skillId" type="info" class="mt-8px" @click.stop="router.push(`/skill/${post.skillId}`)">
                      {{ post.skillTitle }}
                    </NTag>
                  </div>
                  
                  <!-- 交互区 -->
                  <div class="flex text-gray-500">
                    <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary" 
                      @click="handleLike(post)"
                    >
                      <NIcon>
                        <div v-if="post.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                        <div v-else class="i-material-symbols:favorite-outline"></div>
                      </NIcon>
                      <span>{{ post.likeCount || 0 }}</span>
                    </div>
                    
                    <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary"
                      @click="viewPostDetail(post.id)"
                    >
                      <NIcon>
                        <div class="i-material-symbols:comment-outline"></div>
                      </NIcon>
                      <span>{{ post.commentCount || 0 }}</span>
                    </div>
                  </div>
                </NCard>
              </NListItem>
              
              <!-- 加载更多 -->
              <div v-if="postList.length < pagination.total" class="flex justify-center my-16px">
                <NButton :loading="loading" @click="loadMore">加载更多</NButton>
              </div>
              <div v-else-if="postList.length > 0" class="text-center text-gray-400 my-16px">
                没有更多了
              </div>
            </NList>
          </NScrollbar>
        </div>
      </div>
      
      <!-- 右侧 - 推荐 -->
      <div class="w-300px">
        <NCard title="热门技能" :bordered="false" class="shadow-sm mb-16px">
          <NEmpty v-if="false" description="暂无数据" />
          <NList>
            <NListItem v-for="i in 5" :key="i">
              <div class="flex items-center gap-8px cursor-pointer" @click="router.push(`/skill/${i}`)">
                <div class="text-gray-400">#{{ i }}</div>
                <div class="flex-1 truncate">热门技能示例{{ i }}</div>
              </div>
            </NListItem>
          </NList>
        </NCard>
        
        <NCard title="推荐用户" :bordered="false" class="shadow-sm">
          <NEmpty v-if="false" description="暂无数据" />
          <NList>
            <NListItem v-for="i in 5" :key="i">
              <div class="flex items-center gap-12px">
                <NAvatar size="small">用{{ i }}</NAvatar>
                <div class="flex-1 truncate">推荐用户{{ i }}</div>
                <NButton size="tiny" type="primary">关注</NButton>
              </div>
            </NListItem>
          </NList>
        </NCard>
      </div>
    </div>
    
    <!-- 发布动态弹窗 -->
    <NModal v-model:show="showCreatePostModal" preset="card" title="发布动态" style="max-width: 600px;">
      <NForm>
        <NFormItem label="内容">
          <NInput type="textarea" v-model:value="newPostForm.content" :rows="4" placeholder="分享你的技能、经验或想法..." />
        </NFormItem>
        
        <NFormItem label="图片">
          <NUpload
            multiple
            list-type="image-card"
            :max="9"
            :custom-request="customRequest"
          >
            <div style="margin-bottom: 8px;">
              <NIcon size="48" class="text-gray-300">
                <div class="i-material-symbols:cloud-upload"></div>
              </NIcon>
            </div>
            <div>点击或拖拽上传</div>
          </NUpload>
        </NFormItem>
        
        <NFormItem label="关联技能">
          <NSelect v-model:value="newPostForm.skillId" placeholder="选择关联的技能服务" clearable />
        </NFormItem>
        
        <div class="flex justify-end gap-12px">
          <NButton @click="showCreatePostModal = false">取消</NButton>
          <NButton type="primary" :disabled="!newPostForm.content" @click="handleCreatePost">
            发布
          </NButton>
        </div>
      </NForm>
    </NModal>
  </div>
</template>

<style scoped>
.image-grid {
  grid-template-columns: repeat(3, 1fr);
}
.image-item {
  aspect-ratio: 1/1;
  overflow: hidden;
}
</style> 