<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { fetchPostDetail, fetchComments, createComment, likePost, likeComment, followUser } from '@/service/api/social';
import type { Post, Comment } from '@/service/api/social';

defineOptions({
  name: 'SocialPostDetail'
});

const route = useRoute();
const router = useRouter();
const postId = ref(Number(route.params.id));

// 动态详情
const post = ref<Post | null>(null);
const loading = ref(false);

// 评论列表
const comments = ref<Comment[]>([]);
const commentLoading = ref(false);
const commentPagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 发表评论
const commentContent = ref('');
const replyTo = ref<{
  commentId: number;
  userId: number;
  userName: string;
} | null>(null);

// 获取动态详情
async function getPostDetail() {
  loading.value = true;
  try {
    const res = await fetchPostDetail(postId.value);
    post.value = res;
  } catch (error) {
    console.error('获取动态详情失败', error);
  } finally {
    loading.value = false;
  }
}

// 获取评论列表
async function getComments(isRefresh = false) {
  if (isRefresh) {
    commentPagination.page = 1;
  }
  commentLoading.value = true;
  try {
    const res = await fetchComments(postId.value, {
      page: commentPagination.page,
      pageSize: commentPagination.pageSize
    });
    if (res.data?.records) {
      if (isRefresh) {
        comments.value = res.data.records;
      } else {
        comments.value = [...comments.value, ...res.data.records];
      }
      commentPagination.total = res.data.total;
    }
  } catch (error) {
    console.error('获取评论失败', error);
  } finally {
    commentLoading.value = false;
  }
}

// 加载更多评论
function loadMoreComments() {
  if (comments.value.length < commentPagination.total) {
    commentPagination.page += 1;
    getComments();
  }
}

// 点赞/取消点赞动态
async function handleLikePost() {
  if (!post.value) return;
  try {
    await likePost(post.value.id, !post.value.isLiked);
    post.value.isLiked = !post.value.isLiked;
    post.value.likeCount = post.value.isLiked ? post.value.likeCount + 1 : post.value.likeCount - 1;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 点赞/取消点赞评论
async function handleLikeComment(comment: Comment) {
  try {
    await likeComment(comment.id, !comment.isLiked);
    comment.isLiked = !comment.isLiked;
    comment.likeCount = comment.isLiked ? comment.likeCount + 1 : comment.likeCount - 1;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 关注/取消关注用户
async function handleFollow() {
  if (!post.value) return;
  try {
    await followUser(post.value.userId, !post.value.isFollowed);
    post.value.isFollowed = !post.value.isFollowed;
  } catch (error) {
    console.error('操作失败', error);
  }
}

// 回复评论
function replyToComment(comment: Comment) {
  replyTo.value = {
    commentId: comment.id,
    userId: comment.userId,
    userName: comment.userName || ''
  };
  // 聚焦评论输入框
  document.getElementById('comment-input')?.focus();
}

// 取消回复
function cancelReply() {
  replyTo.value = null;
}

// 发表评论
async function submitComment() {
  if (!commentContent.value.trim()) {
    window.$message?.error('评论内容不能为空');
    return;
  }
  
  try {
    await createComment(postId.value, {
      content: commentContent.value,
      parentId: replyTo.value?.commentId,
      replyToUserId: replyTo.value?.userId
    });
    window.$message?.success('评论成功');
    commentContent.value = '';
    replyTo.value = null;
    getComments(true);
    // 刷新动态信息以更新评论数
    getPostDetail();
  } catch (error) {
    console.error('评论失败', error);
  }
}

// 返回上一页
function goBack() {
  router.back();
}

onMounted(() => {
  getPostDetail();
  getComments();
});
</script>

<template>
  <div class="p-24px">
    <div class="mb-16px">
      <NButton @click="goBack">
        <template #icon>
          <NIcon>
            <div class="i-material-symbols:arrow-back"></div>
          </NIcon>
        </template>
        返回
      </NButton>
    </div>
    
    <NSpin :show="loading">
      <div v-if="post" class="flex gap-24px">
        <!-- 左侧 - 动态内容 -->
        <div class="flex-1">
          <NCard :bordered="false" class="shadow-sm mb-16px">
            <!-- 用户信息 -->
            <div class="flex justify-between items-center mb-16px">
              <div class="flex items-center gap-12px cursor-pointer" @click="router.push(`/user/${post.userId}`)">
                <NAvatar v-if="post.userAvatar" :src="post.userAvatar" size="large" />
                <NAvatar v-else size="large">{{ post.userName?.substring(0, 1) || '用' }}</NAvatar>
                <div>
                  <div class="font-medium text-lg">{{ post.userName }}</div>
                  <NTime :time="new Date(post.createdTime)" format="yyyy-MM-dd HH:mm:ss" />
                </div>
              </div>
              <NButton 
                v-if="post.userId !== $auth?.userId" 
                :type="post.isFollowed ? 'default' : 'primary'" 
                @click="handleFollow"
              >
                {{ post.isFollowed ? '已关注' : '关注' }}
              </NButton>
            </div>
            
            <!-- 动态内容 -->
            <div class="mb-24px">
              <div class="text-base whitespace-pre-wrap mb-16px">{{ post.content }}</div>
              
              <!-- 图片 -->
              <div v-if="post.images && post.images.length > 0" class="grid gap-12px mb-16px">
                <div v-for="(img, imgIndex) in post.images" :key="imgIndex">
                  <img :src="img" alt="动态图片" class="max-w-full rounded-4px" />
                </div>
              </div>
              
              <!-- 关联技能 -->
              <NTag v-if="post.skillId" type="info" class="mt-8px" @click="router.push(`/skill/${post.skillId}`)">
                {{ post.skillTitle }}
              </NTag>
            </div>
            
            <!-- 交互区 -->
            <div class="flex border-t border-gray-100 pt-16px text-gray-500">
              <div class="flex-1 flex items-center gap-4px cursor-pointer hover:text-primary" 
                @click="handleLikePost"
              >
                <NIcon>
                  <div v-if="post.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                  <div v-else class="i-material-symbols:favorite-outline"></div>
                </NIcon>
                <span>{{ post.likeCount || 0 }}</span>
              </div>
            </div>
          </NCard>
          
          <!-- 评论区 -->
          <NCard title="评论" :bordered="false" class="shadow-sm">
            <!-- 评论输入框 -->
            <div class="mb-24px">
              <div v-if="replyTo" class="flex items-center mb-8px text-sm text-gray-500">
                <span>回复 @{{ replyTo.userName }}：</span>
                <NButton text size="tiny" @click="cancelReply">取消</NButton>
              </div>
              <NInput
                id="comment-input"
                v-model:value="commentContent"
                type="textarea"
                :rows="3"
                placeholder="写下你的评论..."
              />
              <div class="flex justify-end mt-8px">
                <NButton type="primary" :disabled="!commentContent.trim()" @click="submitComment">
                  发表评论
                </NButton>
              </div>
            </div>
            
            <!-- 评论列表 -->
            <NSpin :show="commentLoading">
              <NEmpty v-if="comments.length === 0" description="暂无评论" />
              <div v-else>
                <div v-for="comment in comments" :key="comment.id" class="py-16px border-b border-gray-100 last:border-0">
                  <!-- 评论用户信息 -->
                  <div class="flex gap-12px">
                    <NAvatar v-if="comment.userAvatar" :src="comment.userAvatar" size="small" />
                    <NAvatar v-else size="small">{{ comment.userName?.substring(0, 1) || '用' }}</NAvatar>
                    <div class="flex-1">
                      <div class="flex items-baseline gap-8px">
                        <span class="font-medium">{{ comment.userName }}</span>
                        <NTime class="text-xs text-gray-400" :time="new Date(comment.createdTime)" format="yyyy-MM-dd HH:mm" />
                      </div>
                      
                      <!-- 回复信息 -->
                      <div v-if="comment.replyToUserName" class="text-sm text-gray-500 mb-4px">
                        回复 <span class="text-primary">@{{ comment.replyToUserName }}</span>
                      </div>
                      
                      <!-- 评论内容 -->
                      <div class="my-8px">{{ comment.content }}</div>
                      
                      <!-- 评论操作 -->
                      <div class="flex gap-16px text-sm text-gray-500">
                        <div class="cursor-pointer hover:text-primary" @click="replyToComment(comment)">回复</div>
                        <div class="cursor-pointer hover:text-primary flex items-center gap-2px" @click="handleLikeComment(comment)">
                          <NIcon size="16">
                            <div v-if="comment.isLiked" class="i-material-symbols:favorite text-red-500"></div>
                            <div v-else class="i-material-symbols:favorite-outline"></div>
                          </NIcon>
                          <span>{{ comment.likeCount || '' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 加载更多评论 -->
                <div v-if="comments.length < commentPagination.total" class="flex justify-center mt-16px">
                  <NButton :loading="commentLoading" @click="loadMoreComments">加载更多</NButton>
                </div>
                <div v-else-if="comments.length > 0" class="text-center text-gray-400 mt-16px">
                  没有更多评论了
                </div>
              </div>
            </NSpin>
          </NCard>
        </div>
        
        <!-- 右侧 - 推荐 -->
        <div class="w-300px">
          <NCard title="相关技能" :bordered="false" class="shadow-sm mb-16px">
            <NEmpty v-if="false" description="暂无相关技能" />
            <NList>
              <NListItem v-for="i in 3" :key="i">
                <div class="flex items-center gap-8px cursor-pointer" @click="router.push(`/skill/${i}`)">
                  <div class="flex-1 truncate">推荐技能{{ i }}</div>
                </div>
              </NListItem>
            </NList>
          </NCard>
          
          <NCard title="热门动态" :bordered="false" class="shadow-sm">
            <NEmpty v-if="false" description="暂无热门动态" />
            <NList>
              <NListItem v-for="i in 5" :key="i">
                <div class="flex items-start gap-8px cursor-pointer" @click="router.push(`/social/post/${i}`)">
                  <div class="flex-1 truncate line-clamp-2 text-sm">热门动态内容示例{{ i }}</div>
                </div>
              </NListItem>
            </NList>
          </NCard>
        </div>
      </div>
      
      <NEmpty v-else description="动态不存在或已删除" />
    </NSpin>
  </div>
</template> 