/* @type */

.button-tab {
  border-color: #e5e7eb;
}

.button-tab_dark {
  border-color: #ffffff3d;
}

.button-tab:hover {
  color: var(--soy-primary-color);
  border-color: var(--soy-primary-color-opacity3);
}

.button-tab_active {
  color: var(--soy-primary-color);
  border-color: var(--soy-primary-color-opacity3);
  background-color: var(--soy-primary-color-opacity1);
}

.button-tab_active_dark {
  background-color: var(--soy-primary-color-opacity2);
}

.button-tab .svg-close:hover {
  font-size: 12px;
  color: #ffffff;
  background-color: var(--soy-primary-color);
}

.button-tab_dark .svg-close:hover {
  color: #000000;
}

.chrome-tab:hover {
  z-index: 9;
}

.chrome-tab_active {
  z-index: 10;
  color: var(--soy-primary-color);
}

.chrome-tab__bg {
  color: transparent;
}

.chrome-tab_active .chrome-tab__bg {
  color: var(--soy-primary-color1);
}

.chrome-tab_active_dark .chrome-tab__bg {
  color: var(--soy-primary-color2);
}

.chrome-tab:hover .chrome-tab__bg {
  color: #dee1e6;
}

.chrome-tab_active:hover .chrome-tab__bg {
  color: var(--soy-primary-color1);
}

.chrome-tab_dark:hover .chrome-tab__bg {
  color: #333333;
}

.chrome-tab_active_dark:hover .chrome-tab__bg {
  color: var(--soy-primary-color2);
}

.chrome-tab .svg-close:hover {
  font-size: 12px;
  color: #ffffff;
  background-color: #9ca3af;
}

.chrome-tab_active .svg-close:hover {
  background-color: var(--soy-primary-color);
}

.chrome-tab_dark .svg-close:hover {
  color: #000000;
}

.chrome-tab_active .chrome-tab-divider {
  opacity: 0;
}

.chrome-tab:hover .chrome-tab-divider {
  opacity: 0;
}

.chrome-tab_dark .chrome-tab-divider {
  background-color: rgba(255, 255, 255, 0.9);
}
