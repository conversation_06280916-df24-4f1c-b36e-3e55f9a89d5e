server {
    listen 80;
    server_name ${DOMAIN};
    
    root /usr/share/nginx/html;
    index index.html;

    # gzip配置
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 9;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # 单页应用路由配置
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store";
    }

    # 静态资源缓存配置
    location /assets {
        expires 7d;
        add_header Cache-Control "public, no-transform";
    }
} 