version: '3.8'

services:
  traefik:
    image: hub.proxy.dmsy.me/library/traefik:v2.10
    container_name: academy-traefik
    command:
      - "--api.insecure=false"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.dnschallenge.acme.dnschallenge=true"
      - "--certificatesresolvers.dnschallenge.acme.dnschallenge.provider=cloudflare"
      - "--certificatesresolvers.dnschallenge.acme.email=${ACME_EMAIL}"
      - "--certificatesresolvers.dnschallenge.acme.storage=/letsencrypt/acme.json"
    environment:
      - CF_API_EMAIL=${CF_API_EMAIL}
      - CF_DNS_API_TOKEN=${CF_DNS_API_TOKEN}
    ports:
      - "22071:443"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "traefik_data:/letsencrypt"
    networks:
      - academy-network
  mysql:
    image: code-academy-manage-system-mysql:v1.0
    container_name: academy-mysql
    ports:
      - "22076:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - academy-network
  app:
    image: code-academy-manage-system-jar:v1.0
    container_name: academy-jar
    depends_on:
      - mysql
      - sso-login
      - redis
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ***********************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: cust3697927!@#
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_DATABASE: 1
      PYTHON_API_BASE_URL: http://sso-login:8000
    expose:
      - "8081"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.academy-jar.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.academy-jar.entrypoints=websecure"
      - "traefik.http.routers.academy-jar.tls=true"
      - "traefik.http.routers.academy-jar.tls.certresolver=dnschallenge"
      - "traefik.http.services.academy-jar.loadbalancer.server.port=8081"
      - "traefik.http.middlewares.cors.headers.accesscontrolallowmethods=GET,POST,PUT,DELETE,OPTIONS"
      - "traefik.http.middlewares.cors.headers.accesscontrolallowheaders=*"
      - "traefik.http.middlewares.cors.headers.accesscontrolalloworiginlist=https://dmsy.me"
      - "traefik.http.middlewares.cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.cors.headers.addvaryheader=true"
      - "traefik.http.routers.academy-jar.middlewares=cors@docker"
    volumes:
      - upload_data:/app/upload
    networks:
      - academy-network
  redis:
    image: hub.proxy.dmsy.me/library/redis:latest
    container_name: academy-redis
    ports:
      - "22072:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - academy-network
  sso-login:
    image: code-academy-manage-system-custssologin:v1.0
    container_name: academy-custssologin
    depends_on:
      - redis
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=1
    ports:
      - "22070:8000"
    networks:
      - academy-network

networks:
  academy-network:
    driver: bridge

volumes:
  mysql_data:
  upload_data:
  redis_data:
  traefik_data: 