FROM hub.proxy.dmsy.me/library/mysql:8.0

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=cust3697927!@#
ENV MYSQL_DATABASE=CodeAcademyManageSystem
ENV TZ=Asia/Shanghai
# 将初始化SQL复制到docker-entrypoint-initdb.d目录
# MySQL镜像会自动执行这个目录下的.sql文件
RUN ls -la
COPY CodeAcademyManageSystem.sql /docker-entrypoint-initdb.d/

# 设置编码，确保在[mysqld]组下添加配置
RUN echo "[mysqld]" > /etc/mysql/my.cnf \
    && echo "character-set-server=utf8mb4" >> /etc/mysql/my.cnf \
    && echo "collation-server=utf8mb4_unicode_ci" >> /etc/mysql/my.cnf

# 暴露端口
EXPOSE 3306