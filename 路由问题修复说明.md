# Vue Router 无限重定向问题修复说明

## 问题分析

错误信息显示：从 `/skill` 到 `/skill/publish` 的导航中检测到可能的无限重定向。

### 🔍 根本原因
路由路径不匹配导致的问题：

1. **路由配置中的路径**：`/skill-publish`
2. **跳转函数中使用的路径**：`/skill/publish`

这种不匹配导致Vue Router无法找到正确的路由，触发了无限重定向循环。

## 修复内容

### 1. 技能发布页面跳转
**修改前**：
```javascript
function goToPublishSkill() {
  router.push('/skill/publish');  // ❌ 错误路径
}
```

**修改后**：
```javascript
function goToPublishSkill() {
  router.push('/skill-publish');  // ✅ 正确路径
}
```

### 2. 技能详情页面跳转
**修改前**：
```javascript
function goToSkillDetail(id: number) {
  router.push(`/skill/${id}`);  // ❌ 路由不存在
}
```

**修改后**：
```javascript
function goToSkillDetail(id: number) {
  router.push(`/skill-detail?id=${id}`);  // ✅ 使用查询参数
}
```

## 路由配置对照

根据 `schoolweb/src/router/elegant/routes.ts` 中的配置：

| 路由名称 | 路径 | 组件 |
|---------|------|------|
| skill | `/skill` | skill/index.vue |
| skill-detail | `/skill-detail` | skill-detail/index.vue |
| skill-publish | `/skill-publish` | skill-publish/index.vue |

## 验证方法

### 1. 检查路由是否正确注册
在浏览器开发者工具中执行：
```javascript
// 查看所有注册的路由
console.log(router.getRoutes().map(r => ({ name: r.name, path: r.path })));
```

### 2. 测试跳转功能
1. 访问 `/skill` 页面
2. 点击"发布我的技能"按钮
3. 应该正确跳转到 `/skill-publish` 页面

### 3. 检查导航守卫日志
在浏览器控制台查看路由守卫的日志输出，确认没有无限循环。

## 可能的其他问题

### 1. 动态路由参数
如果需要使用动态路由参数（如 `/skill/:id`），需要在路由配置中定义：

```typescript
{
  name: 'skill-detail',
  path: '/skill-detail/:id',  // 动态参数
  component: 'layout.base$view.skill-detail',
  meta: {
    title: 'skill-detail',
    i18nKey: 'route.skill-detail'
  }
}
```

### 2. 查询参数 vs 路径参数
- **查询参数**：`/skill-detail?id=123` （当前使用）
- **路径参数**：`/skill-detail/123` （需要修改路由配置）

### 3. 路由守卫问题
确保导航守卫中没有返回错误的重定向路径。

## 后续优化建议

### 1. 统一路由管理
创建一个路由常量文件来管理所有路由路径：

```typescript
// router/constants.ts
export const ROUTES = {
  SKILL: '/skill',
  SKILL_DETAIL: '/skill-detail',
  SKILL_PUBLISH: '/skill-publish'
} as const;
```

### 2. 类型安全的路由跳转
使用路由名称而不是路径进行跳转：

```typescript
// 使用路由名称
router.push({ name: 'skill-publish' });

// 带参数的跳转
router.push({ 
  name: 'skill-detail', 
  query: { id: skillId } 
});
```

### 3. 路由参数验证
在技能详情页面中验证ID参数：

```typescript
const route = useRoute();
const skillId = computed(() => {
  const id = route.query.id;
  return typeof id === 'string' ? parseInt(id) : null;
});
```

## 测试清单

- [ ] 从技能列表页面跳转到发布页面
- [ ] 从技能列表页面跳转到详情页面  
- [ ] 发布成功后跳转回列表页面
- [ ] 浏览器前进后退按钮正常工作
- [ ] 直接访问URL地址正常显示页面
- [ ] 没有控制台错误或警告

修复完成后，Vue Router的无限重定向问题应该得到解决。
