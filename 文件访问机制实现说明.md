# 文件访问机制实现说明

## 概述

项目已经实现了完整的文件上传和访问机制，上传的文件通过后端接口进行访问，确保文件的安全性和统一管理。

## 后端实现

### 1. 文件存储结构

**存储路径**：`${user.dir}/upload/`

**目录结构**：
```
upload/
├── avatar/          # 用户头像
├── card/           # 一卡通照片
├── skillcover/     # 技能封面图
├── skillimage/     # 技能服务图片
└── other/          # 其他文件
```

### 2. 文件上传服务

**FileUploadServiceImpl.java**：
```java
@Override
public String saveFile(MultipartFile file, FilePurposeEnum purpose) throws Exception {
    // 生成唯一文件名：UUID + 时间戳 + 扩展名
    String fileName = UUID.randomUUID().toString().replace("-", "") + 
                     LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + 
                     extension;
    
    // 根据用途创建子目录
    String subDir = purpose.getCode();
    Path purposePath = Paths.get(uploadPath, subDir);
    
    // 返回相对访问路径
    return String.format("/files/%s/%s", subDir, fileName);
}
```

### 3. 静态资源配置

**FileUploadConfig.java**：
```java
@Override
public void addResourceHandlers(ResourceHandlerRegistry registry) {
    registry.addResourceHandler("/files/**")
            .addResourceLocations("file:" + uploadPath + "/");
}
```

**application.yml**：
```yaml
spring:
  web:
    resources:
      static-locations: file:${user.dir}/upload/,classpath:/static/

file:
  upload:
    path: ${user.dir}/upload
```

### 4. 访问权限配置

**SaTokenConfigure.java**：
```java
.excludePathPatterns(
    "/user/login",
    "/user/register", 
    "/user/send-email-code",
    "/user/login-with-code",
    "/files/**"  // 允许文件访问不需要登录
);
```

## 前端实现

### 1. 文件上传API

**file.ts**：
```typescript
export function uploadFile(file: File, purpose: FilePurpose = FilePurpose.OTHER) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('purpose', purpose);
  return request<string>({
    url: '/upload',
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  }).then(res => res.data);
}
```

### 2. 文件URL生成

**file.ts**：
```typescript
export function getFileUrl(fileUrl: string) {
  if (!fileUrl) return '';
  // 如果已经是完整URL，直接返回
  if (fileUrl.startsWith('http')) {
    return fileUrl;
  }
  const baseUrl = import.meta.env.VITE_SERVICE_BASE_URL;
  // 确保路径以/开头
  const path = fileUrl.startsWith('/') ? fileUrl : `/${fileUrl}`;
  // 文件访问不需要token，因为已经在后端配置了白名单
  return `${baseUrl}${path}`;
}
```

### 3. 技能发布页面使用

**上传封面图**：
```typescript
async function handleUploadCover(options: any) {
  const fileUrl = await uploadFile(file.file as File, FilePurpose.SKILL_COVER);
  if (fileUrl) {
    // 存储原始路径用于提交表单
    const originalPath = fileUrl;
    // 生成完整的访问URL用于显示
    const fullUrl = getFileUrl(fileUrl);
    
    coverUrl.value = fullUrl;  // 用于显示
    formData.images.unshift(originalPath);  // 用于提交
  }
}
```

**显示图片**：
```vue
<img :src="getFileUrl(image)" class="w-full h-full object-cover" />
```

## 数据流程

### 1. 文件上传流程

1. **前端选择文件** → 调用 `uploadFile(file, purpose)`
2. **发送请求** → `POST /upload` 带文件和用途参数
3. **后端处理** → 验证文件类型，生成唯一文件名
4. **保存文件** → 存储到 `upload/{purpose}/` 目录
5. **返回路径** → 返回相对路径如 `/files/skillcover/xxx.jpg`
6. **前端接收** → 获得文件访问路径

### 2. 文件访问流程

1. **前端需要显示** → 调用 `getFileUrl(relativePath)`
2. **生成完整URL** → `${baseUrl}/files/skillcover/xxx.jpg`
3. **浏览器请求** → `GET /files/skillcover/xxx.jpg`
4. **Spring处理** → 通过静态资源配置映射到文件系统
5. **返回文件** → 直接返回文件内容

### 3. 表单提交流程

1. **收集数据** → 表单包含文件的相对路径
2. **提交表单** → `images: ["/files/skillcover/xxx.jpg", ...]`
3. **后端存储** → 将路径存储到数据库
4. **前端显示** → 从数据库读取路径，通过 `getFileUrl` 生成访问URL

## 安全考虑

### 1. 文件类型验证

- **后端验证**：通过 `FilePurposeEnum` 限制允许的文件扩展名
- **前端限制**：通过 `accept` 属性限制文件选择

### 2. 文件名安全

- **唯一性**：使用 UUID + 时间戳确保文件名唯一
- **安全性**：不使用原始文件名，避免路径遍历攻击

### 3. 访问控制

- **公开访问**：文件访问不需要登录（适合图片展示）
- **上传权限**：文件上传需要登录验证

### 4. 文件大小限制

```yaml
spring:
  servlet:
    multipart:
      max-file-size: 10MB      # 单个文件最大10MB
      max-request-size: 100MB  # 请求最大100MB
```

## 使用示例

### 1. 上传技能封面图

```typescript
// 上传文件
const fileUrl = await uploadFile(file, FilePurpose.SKILL_COVER);
// 结果："/files/skillcover/abc123def456_20250603194500.jpg"

// 显示图片
const displayUrl = getFileUrl(fileUrl);
// 结果："http://localhost:8080/files/skillcover/abc123def456_20250603194500.jpg"

// 提交表单
const formData = {
  images: [fileUrl]  // 存储相对路径
};
```

### 2. 从数据库读取并显示

```typescript
// 从后端API获取技能详情
const skillDetail = await fetchSkillDetail(id);
// skillDetail.images = ["/files/skillcover/xxx.jpg", "/files/skillimage/yyy.jpg"]

// 在模板中显示
<img v-for="image in skillDetail.images" :src="getFileUrl(image)" />
```

## 优势特点

### 1. 统一管理

- 所有文件通过统一的上传接口处理
- 按用途分类存储，便于管理
- 统一的访问路径格式

### 2. 安全可靠

- 文件类型验证
- 唯一文件名生成
- 访问权限控制

### 3. 易于扩展

- 新增文件用途只需添加枚举值
- 支持不同用途的不同验证规则
- 便于添加文件处理逻辑（如图片压缩）

### 4. 性能优化

- 静态资源直接由Spring处理，性能较好
- 支持浏览器缓存
- 可以轻松集成CDN

## 注意事项

1. **路径存储**：数据库中存储相对路径，便于迁移和配置变更
2. **URL生成**：显示时通过 `getFileUrl` 生成完整URL
3. **文件清理**：需要定期清理未使用的文件（可以通过定时任务实现）
4. **备份策略**：重要文件需要考虑备份策略
5. **CDN集成**：生产环境可以考虑集成CDN加速文件访问
